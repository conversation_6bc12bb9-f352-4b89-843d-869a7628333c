#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增量同步逻辑的脚本
验证只同步上次同步之后新增的附件记录

使用方法：
1. 启动Odoo shell: python odoo-bin shell -d your_database
2. 在shell中运行: exec(open('myaddons/hangxing_release/ylhc_synology_integration/test_incremental_sync.py').read())
"""

def test_incremental_sync_logic():
    """测试增量同步逻辑"""
    print("=== 测试增量同步逻辑 ===")
    
    # 获取模型
    sync_log_model = env['synology.sync.log']
    attachment_model = env['ir.attachment']
    
    # 1. 查看现有的同步日志
    print("\n1. 查看现有的同步日志...")
    
    existing_logs = sync_log_model.search([
        ('model_name', '!=', False),
        ('field_name', '!=', False),
        ('sync_type', 'in', ['scheduled', 'manual', 'api']),
        ('status', 'in', ['success', 'partial'])
    ], order='sync_date desc', limit=5)
    
    print(f"   找到 {len(existing_logs)} 个成功的同步日志:")
    for log in existing_logs:
        print(f"   - 日志 {log.id}: {log.model_name}.{log.field_name}")
        print(f"     同步时间: {log.sync_date}")
        print(f"     状态: {log.status}")
        print(f"     处理数量: {log.total_count}")
        print()
    
    # 2. 模拟查找上次同步时间的逻辑
    print("2. 测试查找上次同步时间的逻辑...")
    
    test_model = 'purchase.order'
    test_field = 'datas'
    
    latest_sync_log = sync_log_model.search([
        ('model_name', '=', test_model),
        ('field_name', '=', test_field),
        ('sync_type', 'in', ['scheduled', 'manual', 'api']),
        ('status', 'in', ['success', 'partial'])
    ], limit=1, order='sync_date desc')
    
    if latest_sync_log:
        last_sync_time = latest_sync_log.sync_date
        print(f"   找到上次同步时间: {last_sync_time}")
        print(f"   来自日志: {latest_sync_log.id}")
    else:
        last_sync_time = '2025-08-02 18:00:00'
        print(f"   未找到上次同步，使用默认时间: {last_sync_time}")
    
    # 3. 测试附件查询逻辑
    print(f"\n3. 测试附件查询逻辑 (只查找 {last_sync_time} 之后的附件)...")
    
    # 查询在上次同步时间之后创建的附件
    domain = [
        ('res_model', '=', test_model),
        ('res_field', '=', test_field),
        ('type', '=', 'binary'),
        '|',
        ('synology_sync_status', '=', False),
        ('synology_sync_status', 'in', ['pending', 'failed']),
        ('create_date', '>', last_sync_time)
    ]
    
    new_attachments = attachment_model.search(domain, limit=10)
    print(f"   找到 {len(new_attachments)} 个需要同步的新附件:")
    
    for att in new_attachments[:5]:  # 只显示前5个
        print(f"   - 附件 {att.id}: {att.name}")
        print(f"     创建时间: {att.create_date}")
        print(f"     同步状态: {att.synology_sync_status}")
        print()
    
    # 4. 对比：查询所有符合条件的附件（不限制时间）
    print("4. 对比：查询所有符合条件的附件（不限制时间）...")
    
    all_domain = [
        ('res_model', '=', test_model),
        ('res_field', '=', test_field),
        ('type', '=', 'binary'),
        '|',
        ('synology_sync_status', '=', False),
        ('synology_sync_status', 'in', ['pending', 'failed'])
    ]
    
    all_attachments = attachment_model.search(all_domain, limit=10)
    print(f"   总共找到 {len(all_attachments)} 个符合条件的附件")
    print(f"   增量同步减少了 {len(all_attachments) - len(new_attachments)} 个附件的处理")
    
    return {
        'last_sync_time': last_sync_time,
        'new_attachments': new_attachments,
        'all_attachments': all_attachments,
        'latest_sync_log': latest_sync_log
    }

def simulate_sync_process():
    """模拟完整的同步过程"""
    print("\n=== 模拟完整的同步过程 ===")
    
    # 1. 创建一个模拟的同步日志
    print("\n1. 创建模拟的上次同步日志...")
    
    sync_log_model = env['synology.sync.log']
    
    # 创建一个"上次"的同步日志，时间设置为1小时前
    from datetime import datetime, timedelta
    one_hour_ago = datetime.now() - timedelta(hours=1)
    
    previous_log = sync_log_model.create({
        'sync_type': 'manual',
        'model_name': 'purchase.order',
        'field_name': 'datas',
        'sync_date': one_hour_ago,
        'status': 'success',
        'total_count': 5,
        'success_count': 5,
        'failed_count': 0,
        'notes': 'Simulated previous sync for testing'
    })
    
    print(f"   创建模拟日志: ID={previous_log.id}, 时间={previous_log.sync_date}")
    
    # 2. 运行测试同步
    print("\n2. 运行测试同步...")
    
    attachment_model = env['ir.attachment']
    
    try:
        result = attachment_model.with_context(
            test_mode=True,
            max_attachments=3
        ).cron_sync_attachments_to_synology()
        
        print("   ✓ 测试同步完成")
        
        # 3. 查看新创建的同步日志
        print("\n3. 查看新创建的同步日志...")
        
        new_logs = sync_log_model.search([
            ('sync_type', '=', 'test'),
            ('create_date', '>', previous_log.create_date)
        ], order='create_date desc', limit=3)
        
        for log in new_logs:
            print(f"   新日志 {log.id}:")
            print(f"     模型.字段: {log.model_name}.{log.field_name}")
            print(f"     同步时间: {log.sync_date}")
            print(f"     处理数量: {log.total_count}")
            if log.notes:
                print(f"     备注: {log.notes}")
            print()
        
        # 4. 验证增量逻辑
        print("4. 验证增量逻辑...")
        
        for log in new_logs:
            if "Using last sync time" in (log.notes or ""):
                print(f"   ✓ 日志 {log.id} 使用了上次同步时间")
            elif "No previous sync found" in (log.notes or ""):
                print(f"   ℹ 日志 {log.id} 未找到上次同步")
            
            if "Filtering attachments created after" in (log.notes or ""):
                print(f"   ✓ 日志 {log.id} 正确过滤了附件时间范围")
        
    except Exception as e:
        print(f"   ✗ 测试同步失败: {str(e)}")
    
    return previous_log

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    sync_log_model = env['synology.sync.log']
    
    # 删除测试日志和模拟日志
    test_logs = sync_log_model.search([
        '|',
        ('sync_type', '=', 'test'),
        ('notes', 'ilike', 'Simulated previous sync for testing')
    ])
    
    count = len(test_logs)
    
    if count > 0:
        test_logs.unlink()
        print(f"   已删除 {count} 个测试/模拟同步日志")
    else:
        print("   没有找到测试数据")

def main():
    """主测试函数"""
    if 'env' not in globals():
        print("请在Odoo shell中运行此脚本")
        print("启动命令: python odoo-bin shell -d your_database")
        return
    
    print("开始测试增量同步逻辑...")
    
    # 1. 测试增量同步逻辑
    result = test_incremental_sync_logic()
    
    # 2. 模拟完整的同步过程
    previous_log = simulate_sync_process()
    
    print("\n=== 测试总结 ===")
    print("增量同步逻辑的关键特点:")
    print("1. 基于上次成功同步的 sync_date 确定时间范围")
    print("2. 只处理在上次同步时间之后创建的附件")
    print("3. 避免重复处理已同步的附件")
    print("4. 提供详细的日志记录和时间跟踪")
    print("5. 支持不同模型和字段的独立增量同步")
    
    # 询问是否清理测试数据
    print("\n是否要清理测试数据？")
    print("取消注释下面的行来清理:")
    print("# cleanup_test_data()")
    
    return {
        'result': result,
        'previous_log': previous_log
    }

if __name__ == '__main__':
    main()
