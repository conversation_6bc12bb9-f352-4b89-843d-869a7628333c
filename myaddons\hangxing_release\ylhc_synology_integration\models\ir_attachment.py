import logging
from odoo import fields, models, api, _
from odoo.exceptions import UserError
import base64
import tempfile
import os
import time
import requests

_logger = logging.getLogger(__name__)

try:
    from synology_api.filestation import FileStation
    from synology_api.base_api import BaseApi
except ImportError:
    _logger.warning("The 'synology_api' library is not installed.")
    FileStation = None
    BaseApi = None


class IrAttachment(models.Model):
    _name = 'ir.attachment'
    _inherit = ['ir.attachment', 'synology.connection.mixin']

    synology_path = fields.Char(string="Synology Path", readonly=True, index=True)
    synology_share_id = fields.Char(string="Synology Share ID", readonly=True, index=True, help="ID of the sharing link in Synology")
    
    # 同步状态字段
    synology_sync_status = fields.Selection([
        ('pending', 'Pending'),
        ('syncing', 'Syncing'),
        ('done', 'Done'),
        ('failed', 'Failed')
    ], string='Sync Status', readonly=True, index=True)
    
    synology_sync_error = fields.Text(
        string='Sync Error',
        readonly=True,
        help='Error message if sync failed'
    )
    
    synology_sync_date = fields.Datetime(
        string='Sync Date',
        readonly=True,
        help='Last sync attempt date'
    )
    
    
    @api.depends('store_fname', 'db_datas', 'url', 'type', 'synology_path', 'file_size')
    def _compute_datas(self):
        """重写计算datas的方法，支持从群晖URL获取数据"""
        if self._context.get('bin_size'):
            for attach in self:
                # 对于群晖附件，确保返回文件大小
                if attach.type == 'url' and attach.synology_path and attach.file_size:
                    attach.datas = str(attach.file_size)
                else:
                    attach.datas = attach.file_size and str(attach.file_size) or '0'
            return
            
        for attach in self:
            if attach.type == 'url' and attach.synology_path:
                # 对于群晖URL类型的附件，返回一个非空的占位符
                # 这样二进制字段widget会认为有文件存在
                try:
                    if attach.url:
                        # 返回一个最小的base64编码的占位符，让widget认为有内容
                        # 这是一个1x1像素的透明PNG图片的base64编码
                        placeholder = b"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                        attach.datas = placeholder.decode('ascii')
                        _logger.debug(f"Attachment {attach.name} is stored in Synology at {attach.url}")
                    else:
                        attach.datas = False
                except Exception as e:
                    _logger.error(f"Error accessing Synology file: {e}")
                    attach.datas = False
            else:
                # 调用父类的默认行为
                super(IrAttachment, attach)._compute_datas()
    
    @api.depends('store_fname', 'db_datas', 'url', 'type', 'synology_path')
    def _compute_raw(self):
        """重写计算raw的方法，支持从群晖URL获取数据"""
        for attach in self:
            if attach.type == 'url' and attach.synology_path:
                # 对于群晖URL类型的附件，不提供raw数据
                # 避免自动下载大文件
                attach.raw = False
            else:
                # 调用父类的默认行为
                super(IrAttachment, attach)._compute_raw()
    
    def _inverse_datas(self):
        """重写设置datas的方法"""
        for attach in self:
            if attach.type == 'url' and attach.synology_path:
                # 对于已经存储在群晖的文件，不允许通过datas字段更新
                _logger.warning(f"Cannot update datas for Synology attachment {attach.name}")
            else:
                super(IrAttachment, attach)._inverse_datas()
    
    def _get_synology_connection(self):
        """获取群晖连接参数"""
        config = self.env['ir.config_parameter'].sudo()
        ip_address = config.get_param('ylhc_synology_integration.ip_address')
        port = config.get_param('ylhc_synology_integration.port')
        username = config.get_param('ylhc_synology_integration.username')
        password = config.get_param('ylhc_synology_integration.password')
        secure = config.get_param('ylhc_synology_integration.secure', False)
        dest_path = config.get_param('ylhc_synology_integration.destination_path')
        
        _logger.info(f"[Synology] Connection params - IP: {ip_address}, Port: {port}, Username: {username}, Path: {dest_path}")
        
        if not all([ip_address, port, username, password, dest_path]):
            missing = []
            if not ip_address:
                missing.append('ip_address')
            if not port:
                missing.append('port')
            if not username:
                missing.append('username')
            if not password:
                missing.append('password')
            if not dest_path:
                missing.append('destination_path')
            _logger.warning(f"[Synology] Missing connection parameters: {', '.join(missing)}")
            return None
            
        return {
            'ip': ip_address,
            'port': port,
            'username': username,
            'password': password,
            'secure': secure,
            'dest_path': dest_path
        }

    def _get_smart_filename(self, original_name, res_model, res_id, res_field):
        """智能获取文件名"""
        import uuid
        import mimetypes

        # 如果原始文件名就是字段名，需要获取更好的文件名
        if res_field and original_name == res_field:
            # 查找对应的字段配置
            field_config = self.env['synology.attachment.config.field'].search([
                ('field_name', '=', res_field),
                ('config_id.model_name', '=', res_model),
                ('active', '=', True)
            ], limit=1)

            if field_config and field_config.filename_field:
                # 尝试从源记录获取文件名
                try:
                    if res_model and res_id:
                        record = self.env[res_model].sudo().browse(res_id)
                        if record.exists() and hasattr(record, field_config.filename_field):
                            filename_from_record = getattr(record, field_config.filename_field)
                            if filename_from_record:
                                # 清理文件名，移除非法字符
                                clean_filename = self._clean_filename(filename_from_record)
                                if clean_filename:
                                    return clean_filename
                except Exception as e:
                    _logger.warning(f"Failed to get filename from record {res_model}:{res_id}.{field_config.filename_field}: {str(e)}")

            # 如果无法从记录获取文件名，生成随机文件名
            return self._generate_random_filename(res_model, res_field, res_id)

        # 如果原始文件名不是字段名，直接使用（但要清理）
        return self._clean_filename(original_name) or self._generate_random_filename(res_model, res_field, res_id)

    def _clean_filename(self, filename):
        """清理文件名，移除非法字符"""
        if not filename:
            return None

        # 移除路径分隔符和其他非法字符
        clean_name = filename.replace('/', '_').replace('\\', '_').replace(':', '_')
        clean_name = clean_name.replace('?', '_').replace('*', '_').replace('<', '_')
        clean_name = clean_name.replace('>', '_').replace('|', '_').replace('"', '_')

        # 移除多余的空格和点
        clean_name = clean_name.strip(' .')

        return clean_name if clean_name else None

    def _generate_random_filename(self, res_model, res_field, res_id):
        """生成随机文件名，带正确的扩展名"""
        import uuid
        import mimetypes

        # 生成基础文件名
        random_name = str(uuid.uuid4())[:8]

        # 添加模型和记录信息
        if res_model and res_id:
            model_short = res_model.split('.')[-1]  # 取模型名的最后部分
            base_name = f"{model_short}_{res_id}_{random_name}"
        else:
            base_name = f"file_{random_name}"

        # 尝试根据附件的mimetype确定扩展名
        extension = '.bin'  # 默认扩展名

        if self.mimetype:
            # 根据mimetype猜测扩展名
            ext = mimetypes.guess_extension(self.mimetype)
            if ext:
                extension = ext
            elif 'pdf' in self.mimetype.lower():
                extension = '.pdf'
            elif 'image' in self.mimetype.lower():
                if 'jpeg' in self.mimetype.lower() or 'jpg' in self.mimetype.lower():
                    extension = '.jpg'
                elif 'png' in self.mimetype.lower():
                    extension = '.png'
                elif 'gif' in self.mimetype.lower():
                    extension = '.gif'
            elif 'word' in self.mimetype.lower() or 'msword' in self.mimetype.lower():
                extension = '.docx' if 'openxml' in self.mimetype.lower() else '.doc'
            elif 'excel' in self.mimetype.lower() or 'spreadsheet' in self.mimetype.lower():
                extension = '.xlsx' if 'openxml' in self.mimetype.lower() else '.xls'
            elif 'powerpoint' in self.mimetype.lower() or 'presentation' in self.mimetype.lower():
                extension = '.pptx' if 'openxml' in self.mimetype.lower() else '.ppt'
            elif 'text' in self.mimetype.lower():
                extension = '.txt'

        return f"{base_name}{extension}"

    def _upload_to_synology(self, name, datas, res_model, res_id, res_field=None):
        """上传文件到群晖并返回分享链接"""
        if not FileStation:
            _logger.error("Synology API not available")
            return None

        params = self._get_synology_connection()
        if not params:
            _logger.error("Synology connection parameters not configured")
            return None
            
        try:
            # 解码文件内容
            file_content = base64.b64decode(datas)

            # 智能处理文件名
            final_filename = self._get_smart_filename(name, res_model, res_id, res_field)

            # 如果文件名发生了变化，更新附件记录的name字段，保持Odoo和NAS一致
            if final_filename != name:
                self.write({'name': final_filename})
                _logger.info(f"Updated attachment name from '{name}' to '{final_filename}' for consistency")

            # 获取目标路径
            base_dest_path = "/" + params['dest_path'].strip("/")

            # 获取子文件夹路径
            record_name = 'undefined_record'
            if res_model and res_id:
                try:
                    record = self.env[res_model].sudo().browse(res_id)
                    if record.exists():
                        record_name = record.display_name or f"record_{res_id}"
                        record_name = record_name.replace('/', '_').replace('\\', '_').replace(':', '_').replace('?', '_').replace('*', '_')
                except:
                    pass
            
            # 从配置获取子文件夹模式
            synology_config = self.env['synology.attachment.config'].sudo().get_config_for_model(res_model)
            if synology_config:
                subfolder = synology_config.get_subfolder_path(res_id, record_name)
            else:
                model_name = res_model.replace('.', '_') if res_model else 'undefined_model'
                subfolder = f"{model_name}/{record_name}"
            
            dest_path = os.path.join(base_dest_path, subfolder).replace('\\', '/')
            
            # 创建临时文件
            temp_file_path = os.path.join(tempfile.gettempdir(), final_filename)
            with open(temp_file_path, 'wb') as temp_file:
                temp_file.write(file_content)

            try:
                # 使用连接管理器上传文件
                connection_manager = self._get_synology_connection_manager()

                # 上传文件
                upload_result = connection_manager.upload_file(params, dest_path, temp_file_path)
                if isinstance(upload_result, dict) and not upload_result.get('success'):
                    raise Exception(f"Upload failed: {upload_result.get('error')}")

                time.sleep(0.5)  # 减少等待时间

                # 创建分享链接
                synology_file_path = f"{dest_path}/{final_filename}"
                sharing_link_result = connection_manager.create_sharing_link(params, synology_file_path)
                if not sharing_link_result or not sharing_link_result.get('success'):
                    raise Exception(f"Link creation failed: {sharing_link_result.get('error')}")
                
                link_data = sharing_link_result['data']['links'][0]
                sharing_url = link_data['url']
                sharing_id = link_data.get('id', '')
                
                _logger.info("Successfully uploaded to Synology - Path: %s, URL: %s, ID: %s" % 
                           (synology_file_path, sharing_url, sharing_id))
                
                return {
                    'url': sharing_url,
                    'path': synology_file_path,
                    'share_id': sharing_id
                }
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            _logger.error("Failed to upload to Synology: %s" % str(e))
            return None
    
    @api.model
    def get_synology_attachment_for_field(self, res_model, res_id, res_field):
        """获取字段对应的附件记录ID，优先返回群晖附件"""
        # 优先查找群晖附件（type='url' 且有 synology_path）
        synology_attachment = self.search([
            ('res_model', '=', res_model),
            ('res_id', '=', res_id),
            ('res_field', '=', res_field),
            ('type', '=', 'url'),
            ('synology_path', '!=', False)
        ], limit=1)
        
        if synology_attachment:
            return synology_attachment.ids
        
        # 如果没有群晖附件，查找任何已存在的附件
        existing = self.search([
            ('res_model', '=', res_model),
            ('res_id', '=', res_id),
            ('res_field', '=', res_field)
        ], limit=1)
        
        if existing:
            return existing.ids
        
        # 不再自动创建附件，只返回空列表
        return []
    
    def write(self, vals):
        """重写 write 方法"""
        # 移除了即时上传到群晖的逻辑
        # 现在所有上传都通过 sync_to_synology() 方法或定时任务处理
        return super(IrAttachment, self).write(vals)
    
    def sync_to_synology(self):
        """同步附件到群晖NAS"""
        for attachment in self:
            if attachment.type != 'binary' or attachment.synology_path:
                # 跳过非二进制或已同步的附件
                continue
                
            try:
                # 标记为同步中
                attachment.write({
                    'synology_sync_status': 'syncing',
                    'synology_sync_date': fields.Datetime.now(),
                    'synology_sync_error': False
                })
                
                # 获取文件数据
                file_data = attachment.datas
                if not file_data:
                    raise Exception("No file data found")
                
                # 上传到群晖
                result = attachment._upload_to_synology(
                    attachment.name,
                    file_data,
                    attachment.res_model,
                    attachment.res_id,
                    attachment.res_field
                )
                
                if result:
                    # 更新附件记录
                    attachment.write({
                        'type': 'url',
                        'url': result['url'],
                        'synology_path': result['path'],
                        'synology_share_id': result['share_id'],
                        'synology_sync_status': 'done',
                        'store_fname': False,  # 清除本地存储
                        'db_datas': False
                    })
                    _logger.info(f"Successfully synced attachment {attachment.name} to Synology")
                else:
                    raise Exception("Upload to Synology failed")
                    
            except Exception as e:
                _logger.error(f"Failed to sync attachment {attachment.name}: {str(e)}")
                attachment.write({
                    'synology_sync_status': 'failed',
                    'synology_sync_error': str(e)
                })

    def batch_sync_to_synology_with_log(self):
        """批量同步附件到群晖NAS并记录日志"""
        import time
        start_time = time.time()

        # 创建同步日志
        sync_log = self.env['synology.sync.log'].create_sync_log(
            sync_type='manual',
            model_name=self._name
        )

        attachment_results = {}

        try:
            for attachment in self:
                try:
                    # 获取文件大小
                    file_size_mb = 0
                    if attachment.file_size:
                        file_size_mb = attachment.file_size / (1024 * 1024)
                    elif attachment.datas:
                        file_size_mb = len(base64.b64decode(attachment.datas)) / (1024 * 1024)

                    # 执行同步
                    attachment.sync_to_synology()

                    # 记录结果
                    if attachment.synology_sync_status == 'done':
                        attachment_results[attachment.id] = {
                            'success': True,
                            'synology_path': attachment.synology_path,
                            'url': attachment.url,
                            'size_mb': file_size_mb
                        }
                    else:
                        attachment_results[attachment.id] = {
                            'success': False,
                            'error': attachment.synology_sync_error or 'Unknown error',
                            'name': attachment.name
                        }

                except Exception as e:
                    attachment_results[attachment.id] = {
                        'success': False,
                        'error': str(e),
                        'name': attachment.name or 'Unknown'
                    }

            # 更新同步日志
            sync_log.update_results(attachment_results)
            sync_log.set_duration(start_time)

            # 添加备注
            success_count = sum(1 for r in attachment_results.values() if r['success'])
            failed_count = len(attachment_results) - success_count
            sync_log.add_note(f"Manual batch sync completed: {success_count} successful, {failed_count} failed")

        except Exception as e:
            sync_log.add_note(f"Batch sync failed with error: {str(e)}")
            sync_log.set_duration(start_time)
            raise

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Sync Complete',
                'message': f'Synced {len(self)} attachments. Check sync logs for details.',
                'type': 'success',
            }
        }

    def test_sync_purchase_order_2619(self):
        """测试方法：同步采购订单2619的附件到群晖"""
        # 方法1：通过SQL直接查询
        # self.env.cr.execute("""
        #     SELECT id FROM ir_attachment 
        #     WHERE res_model = 'purchase.order' 
        #     AND res_id = 2619 
        #     AND type = 'binary'
        # """)
        # attachment_ids = [row[0] for row in self.env.cr.fetchall()]
        
        # _logger.info(f"Found {len(attachment_ids)} attachment IDs via SQL: {attachment_ids}")
        
        # 方法2：显式包含 res_field 条件，使用 OR 来包含所有情况
        domain = [
            ('res_model', '=', 'purchase.order'),
            ('res_id', '=', 2619),
            ('type', '=', 'binary'),
            '|',
            ('res_field', '=', False),
            ('res_field', '!=', False),  # 显式包含有 res_field 的记录
        ]
        
        attachments = self.env['ir.attachment'].sudo().search(domain)
        _logger.info(f"Found {len(attachments)} attachments via search with res_field condition")
        
        # 如果通过 search 找不到，使用 browse 直接访问
        # if not attachments and attachment_ids:
        #     attachments = self.env['ir.attachment'].sudo().browse(attachment_ids)
        #     _logger.info(f"Using browse to access {len(attachments)} attachments")
        
        if attachments:
            _logger.info(f"Found {len(attachments)} attachments for purchase.order #2619")
            for attachment in attachments:
                _logger.info(f"Attachment: {attachment.name}, Field: {attachment.res_field}, Status: {attachment.synology_sync_status}")
                try:
                    attachment.sync_to_synology()
                except Exception as e:
                    _logger.error(f"Error syncing attachment {attachment.id}: {str(e)}")
        else:
            _logger.info("No attachments found for purchase.order #2619 that need syncing")
        
        return True
    
    @api.model
    def cron_sync_attachments_to_synology(self):
        """定时任务：同步附件到群晖"""
        import time
        start_time = time.time()

        # 检查是否为测试模式
        test_mode = self.env.context.get('test_mode', False)
        max_attachments = self.env.context.get('max_attachments', 100)

        # 确定同步类型
        sync_type = 'test' if test_mode else 'scheduled'

        # 创建统一的同步日志
        sync_log = self.env['synology.sync.log'].create_sync_log(
            sync_type=sync_type,
            model_name='ir.attachment'
        )

        if test_mode:
            sync_log.add_note(f"Running in test mode, max attachments: {max_attachments}")

        # 用于汇总所有处理结果
        total_processed = 0
        total_success = 0
        total_failed = 0
        all_attachment_results = {}

        try:
            # 获取所有需要同步的配置
            configs = self.env['synology.attachment.config'].search([
                ('active', '=', True),
                ('upload_to_synology', '=', True)
            ])

            for config in configs:
                # 获取该模型的所有字段配置
                for field_config in config.field_ids.filtered('active'):

                    # 设置时间基准：统一从指定时间开始处理
                    default_start_time = '2025-08-02 10:00:00'

                    if not field_config.sync_existing:
                        # 增量同步：查找上次成功的同步日志，使用sync_date（同步执行时间）
                        latest_sync_log = self.env['synology.sync.log'].search([
                            ('model_name', '=', config.model_name),
                            ('field_name', '=', field_config.field_name),
                            ('sync_type', 'in', ['scheduled', 'manual', 'api']),
                            ('status', 'in', ['success', 'partial'])  # 只考虑成功的同步
                        ], limit=1, order='sync_date desc')

                        if latest_sync_log:
                            last_sync_time = latest_sync_log.sync_date
                            sync_log.add_note(f"Incremental sync: using last sync time {last_sync_time} from log {latest_sync_log.id}")
                        else:
                            # 如果没有成功的同步日志，使用默认起始时间
                            last_sync_time = default_start_time
                            sync_log.add_note(f"Incremental sync: no previous sync found, using default start time {last_sync_time}")
                    else:
                        # 全量同步：也从默认起始时间开始，不处理更早的附件
                        last_sync_time = default_start_time
                        sync_log.add_note(f"Full sync: processing all attachments after {last_sync_time} (sync_existing=True)")
                    # 构建查询条件：只查找需要同步的附件
                    domain = [
                        ('res_model', '=', config.model_name),
                        ('res_field', '=', field_config.field_name),
                        ('type', '=', 'binary'),
                        '|',
                        ('synology_sync_status', '=', False),
                        ('synology_sync_status', 'in', ['pending', 'failed']),
                        ('create_date', '>', last_sync_time)  # 统一添加时间过滤，不处理指定时间之前的附件
                    ]

                    # 查找需要同步的附件
                    limit = max_attachments if test_mode else 100
                    attachments = self.search(domain, limit=limit)  # 批量处理

                    # 如果通过 search 找不到，尝试使用 SQL 直接查询
                    if not attachments:
                        # 构建 SQL 查询
                        sql_conditions = [
                            "res_model = %s",
                            "res_field = %s",
                            "type = 'binary'",
                            "(synology_sync_status IS NULL OR synology_sync_status = 'pending' OR synology_sync_status = 'failed')"
                        ]
                        sql_params = [config.model_name, field_config.field_name]

                        # 统一添加时间限制，不处理指定时间之前的附件
                        sql_conditions.append("create_date > %s")
                        sql_params.append(last_sync_time)

                        sql_query = f"""
                            SELECT id FROM ir_attachment
                            WHERE {' AND '.join(sql_conditions)}
                            ORDER BY create_date ASC
                            LIMIT {limit}
                        """

                        self.env.cr.execute(sql_query, tuple(sql_params))
                        attachment_ids = [row[0] for row in self.env.cr.fetchall()]

                        if attachment_ids:
                            _logger.warning(f"Odoo search failed, using SQL fallback. Found {len(attachment_ids)} attachments for {config.model_name}.{field_config.field_name}")
                            attachments = self.env['ir.attachment'].sudo().browse(attachment_ids)

                    if attachments:
                        _logger.info(f"Found {len(attachments)} attachments to sync for {config.model_name}.{field_config.field_name}")

                        # 检查文件大小和扩展名
                        for attachment in attachments:
                            total_processed += 1
                            try:
                                # 获取文件大小
                                file_size_mb = 0
                                if attachment.file_size:
                                    file_size_mb = attachment.file_size / (1024 * 1024)
                                elif attachment.datas:
                                    file_size_mb = len(base64.b64decode(attachment.datas)) / (1024 * 1024)

                                # 检查是否应该同步
                                if field_config.should_sync_file(attachment.name, file_size_mb):
                                    attachment.sync_to_synology()
                                    if attachment.synology_sync_status == 'done':
                                        total_success += 1
                                        all_attachment_results[attachment.id] = {
                                            'success': True,
                                            'synology_path': attachment.synology_path,
                                            'url': attachment.url,
                                            'size_mb': file_size_mb
                                        }
                                    else:
                                        total_failed += 1
                                        all_attachment_results[attachment.id] = {
                                            'success': False,
                                            'error': attachment.synology_sync_error or 'Unknown error',
                                            'name': attachment.name
                                        }
                                else:
                                    # 标记为跳过
                                    attachment.write({
                                        'synology_sync_status': 'done',
                                        'synology_sync_error': 'Skipped by file filter'
                                    })
                                    all_attachment_results[attachment.id] = {
                                        'success': True,
                                        'synology_path': 'Skipped',
                                        'url': '',
                                        'size_mb': 0
                                    }
                            except Exception as e:
                                total_failed += 1
                                _logger.error(f"Error processing attachment {attachment.id}: {str(e)}")
                                all_attachment_results[attachment.id] = {
                                    'success': False,
                                    'error': str(e),
                                    'name': attachment.name or 'Unknown'
                                }


            # 更新统一的同步日志
            sync_log.update_results(all_attachment_results)
            sync_log.set_duration(start_time)
            sync_log.add_note(f"Processed {total_processed} attachments, {total_success} successful, {total_failed} failed")

        except Exception as e:
            # 记录异常到同步日志
            sync_log.add_note(f"Sync failed with error: {str(e)}")
            sync_log.set_duration(start_time)
            _logger.error(f"Cron sync failed: {str(e)}")
            raise

        return True
    
    
    def _delete_synology_sharing_link(self, connection_manager, params, share_id, file_path):
        """尝试删除群晖分享链接的辅助方法"""
        if not share_id:
            return False

        _logger.info("Attempting to delete sharing link - ID: %s, Path: %s" % (share_id, file_path))

        # 方法1: 使用列表格式的ID
        try:
            result = connection_manager.delete_shared_link(params, [share_id])
            _logger.info("delete_shared_link([%s]) result: %s" % (share_id, str(result)))
            if result and result.get('success'):
                _logger.info("Successfully deleted sharing link %s using list format" % share_id)
                return True
        except Exception as e:
            _logger.warning("delete_shared_link([%s]) failed: %s" % (share_id, str(e)))

        # 方法2: 使用字符串格式的ID
        try:
            result = connection_manager.delete_shared_link(params, share_id)
            _logger.info("delete_shared_link('%s') result: %s" % (share_id, str(result)))
            if result and result.get('success'):
                _logger.info("Successfully deleted sharing link %s using string format" % share_id)
                return True
        except Exception as e:
            _logger.warning("delete_shared_link('%s') failed: %s" % (share_id, str(e)))

        # 方法3: 尝试使用逗号分隔的字符串格式
        try:
            result = connection_manager.delete_shared_link(params, str(share_id))
            _logger.info("delete_shared_link(str(%s)) result: %s" % (share_id, str(result)))
            if result and result.get('success'):
                _logger.info("Successfully deleted sharing link %s using str format" % share_id)
                return True
        except Exception as e:
            _logger.warning("delete_shared_link(str(%s)) failed: %s" % (share_id, str(e)))

        _logger.error("All methods failed to delete sharing link: %s" % share_id)
        return False

    def unlink(self):
        """删除附件时同时删除群晖上的文件"""
        # 先收集需要删除的群晖路径和分享链接ID
        synology_data_to_delete = []
        for rec in self:
            if rec.synology_path:
                synology_data_to_delete.append({
                    'path': rec.synology_path,
                    'share_id': rec.synology_share_id,
                    'url': rec.url if rec.type == 'url' else None
                })

        # 先尝试删除群晖上的分享链接和文件，如果失败则阻止删除 Odoo 记录
        if synology_data_to_delete:
            params = self._get_synology_connection()
            if not params:
                _logger.warning("Synology credentials are not configured. Skipping deletion of files on NAS.")
                # 如果没有配置群晖连接，允许删除 Odoo 记录
                return super(IrAttachment, self).unlink()

            try:
                _logger.info("Connecting to Synology to delete %d files and their sharing links." % len(synology_data_to_delete))
                
                # 使用连接管理器
                connection_manager = self._get_synology_connection_manager()

                # 首先尝试删除分享链接
                share_ids_to_delete = [item['share_id'] for item in synology_data_to_delete if item['share_id']]
                if share_ids_to_delete:
                    _logger.info("Found %d sharing links to delete: %s" % (len(share_ids_to_delete), share_ids_to_delete))

                    # 删除分享链接
                    for i, share_id in enumerate(share_ids_to_delete):
                        file_path = synology_data_to_delete[i]['path'] if i < len(synology_data_to_delete) else 'unknown'
                        self._delete_synology_sharing_link(connection_manager, params, share_id, file_path)

                    # 清理无效的分享链接
                    try:
                        _logger.info("Attempting to clear invalid sharing links as final backup")
                        clear_result = connection_manager.clear_invalid_shared_link(params)
                        _logger.info("Clear invalid sharing links result: %s" % str(clear_result))
                    except Exception as clear_error:
                        _logger.warning("Could not clear invalid sharing links: %s" % str(clear_error))
                else:
                    _logger.info("No sharing links to delete")

                # 然后删除文件
                synology_paths_to_delete = [item['path'] for item in synology_data_to_delete]
                delete_result = connection_manager.delete_blocking_function(params, synology_paths_to_delete)

                if isinstance(delete_result, dict) and not delete_result.get('success'):
                    _logger.error("Failed to delete files on Synology. Response: %s" % str(delete_result))
                    # 群晖删除失败，抛出异常阻止删除 Odoo 记录
                    raise UserError(_("Failed to delete files from Synology NAS. Please check the connection and try again."))
                else:
                    _logger.info("Successfully deleted files from Synology: %s" % str(synology_paths_to_delete))

                _logger.info("Synology operations completed")

            except Exception as e:
                _logger.error("Could not delete files on Synology. Reason: %s" % str(e))
                # 群晖删除失败，抛出异常阻止删除 Odoo 记录
                raise UserError(_("Failed to delete files from Synology NAS: %s") % str(e))

        # 群晖文件和分享链接删除成功后，再删除 Odoo 中的记录
        return super(IrAttachment, self).unlink()