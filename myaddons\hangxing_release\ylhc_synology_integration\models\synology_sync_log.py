# -*- coding: utf-8 -*-
from odoo import models, fields, api
import json
import logging
from datetime import timedelta

_logger = logging.getLogger(__name__)


class SynologySyncLog(models.Model):
    """群晖同步日志记录"""
    
    _name = 'synology.sync.log'
    _description = 'Synology Sync Log'
    _order = 'create_date desc'
    _rec_name = 'create_date'
    
    # 基本信息
    sync_type = fields.Selection([
        ('manual', 'Manual Sync'),
        ('scheduled', 'Scheduled Sync'),
        ('api', 'API Sync'),
        ('test', 'Test Sync')
    ], string='Sync Type', required=True, default='manual')
    
    sync_date = fields.Datetime(
        string='Sync Date',
        required=True,
        default=fields.Datetime.now,
        index=True
    )
    
    # 同步范围
    model_name = fields.Char(
        string='Model',
        index=True,
        help='The model name of synchronized records'
    )
    
    field_name = fields.Char(
        string='Field',
        help='The field name if syncing specific field'
    )
    
    res_ids = fields.Text(
        string='Record IDs',
        help='JSON list of record IDs that were attempted to sync'
    )
    
    attachment_ids = fields.Text(
        string='Attachment IDs',
        help='JSON list of attachment IDs that were processed'
    )
    
    # 同步结果
    total_count = fields.Integer(
        string='Total Count',
        help='Total number of attachments processed'
    )
    
    success_count = fields.Integer(
        string='Success Count',
        help='Number of successfully synced attachments'
    )
    
    failed_count = fields.Integer(
        string='Failed Count',
        help='Number of failed attachments'
    )
    
    skipped_count = fields.Integer(
        string='Skipped Count',
        help='Number of skipped attachments'
    )
    
    # 详细信息
    success_details = fields.Text(
        string='Success Details',
        help='JSON object with successful sync details'
    )
    
    error_details = fields.Text(
        string='Error Details',
        help='JSON object with error details for failed syncs'
    )
    
    # 性能信息
    duration = fields.Float(
        string='Duration (seconds)',
        help='Total time taken for the sync operation'
    )
    
    total_size = fields.Float(
        string='Total Size (MB)',
        help='Total size of files synced in MB'
    )
    
    # 触发信息
    triggered_by = fields.Many2one(
        'res.users',
        string='Triggered By',
        default=lambda self: self.env.user
    )
    
    config_id = fields.Many2one(
        'synology.attachment.config',
        string='Config Used',
        help='The configuration record used for this sync'
    )
    
    # 其他信息
    notes = fields.Text(
        string='Notes',
        help='Additional notes or context about this sync'
    )
    
    # 计算字段
    success_rate = fields.Float(
        string='Success Rate (%)',
        compute='_compute_success_rate',
        store=True
    )
    
    status = fields.Selection([
        ('success', 'Success'),
        ('partial', 'Partial Success'),
        ('failed', 'Failed')
    ], string='Status', compute='_compute_status', store=True)
    
    @api.depends('total_count', 'success_count')
    def _compute_success_rate(self):
        for record in self:
            if record.total_count > 0:
                record.success_rate = (record.success_count / record.total_count) * 100
            else:
                record.success_rate = 0
    
    @api.depends('total_count', 'success_count', 'failed_count')
    def _compute_status(self):
        for record in self:
            if record.total_count == 0:
                record.status = 'success'
            elif record.failed_count == 0:
                record.status = 'success'
            elif record.success_count == 0:
                record.status = 'failed'
            else:
                record.status = 'partial'
    
    def get_res_ids_list(self):
        """获取记录ID列表"""
        if self.res_ids:
            try:
                return json.loads(self.res_ids)
            except:
                return []
        return []
    
    def get_attachment_ids_list(self):
        """获取附件ID列表"""
        if self.attachment_ids:
            try:
                return json.loads(self.attachment_ids)
            except:
                return []
        return []
    
    def get_success_details_dict(self):
        """获取成功详情字典"""
        if self.success_details:
            try:
                return json.loads(self.success_details)
            except:
                return {}
        return {}
    
    def get_error_details_dict(self):
        """获取错误详情字典"""
        if self.error_details:
            try:
                return json.loads(self.error_details)
            except:
                return {}
        return {}
    
    @api.model
    def create_sync_log(self, sync_type, model_name=None, field_name=None, config_id=None):
        """创建同步日志的便捷方法"""
        return self.create({
            'sync_type': sync_type,
            'model_name': model_name,
            'field_name': field_name,
            'config_id': config_id,
            'sync_date': fields.Datetime.now(),
        })
    
    def update_results(self, attachment_results):
        """更新同步结果"""
        success_ids = []
        failed_ids = []
        success_details = {}
        error_details = {}
        total_size = 0

        for att_id, result in attachment_results.items():
            if result.get('success'):
                success_ids.append(att_id)
                success_details[str(att_id)] = {
                    'synology_path': result.get('synology_path'),
                    'url': result.get('url'),
                    'size_mb': result.get('size_mb', 0)
                }
                total_size += result.get('size_mb', 0)
            else:
                failed_ids.append(att_id)
                error_details[str(att_id)] = {
                    'error': str(result.get('error', 'Unknown error')),
                    'name': result.get('name', 'Unknown')
                }

        self.write({
            'total_count': len(attachment_results),
            'success_count': len(success_ids),
            'failed_count': len(failed_ids),
            'success_details': json.dumps(success_details),
            'error_details': json.dumps(error_details),
            'total_size': total_size,
            'attachment_ids': json.dumps(list(attachment_results.keys()))
        })

    def set_duration(self, start_time):
        """设置同步持续时间"""
        import time
        duration = time.time() - start_time
        self.write({'duration': duration})
        return duration

    def add_note(self, note):
        """添加备注"""
        current_notes = self.notes or ""
        if current_notes:
            current_notes += "\n"
        current_notes += f"[{fields.Datetime.now()}] {note}"
        self.write({'notes': current_notes})

    @api.model
    def cleanup_old_logs(self, days=30):
        """清理旧的同步日志"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days)
        old_logs = self.search([('sync_date', '<', cutoff_date)])
        count = len(old_logs)
        old_logs.unlink()
        _logger.info(f"Cleaned up {count} sync logs older than {days} days")
        return count

    def action_view_attachments(self):
        """查看相关的附件"""
        attachment_ids = self.get_attachment_ids_list()
        if not attachment_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Attachments',
                    'message': 'No attachments found for this sync log.',
                    'type': 'warning',
                }
            }

        return {
            'type': 'ir.actions.act_window',
            'name': 'Related Attachments',
            'res_model': 'ir.attachment',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', attachment_ids)],
            'context': {'create': False}
        }