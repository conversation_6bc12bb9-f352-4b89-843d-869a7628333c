#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一同步日志逻辑的脚本
验证一个同步任务只创建一个日志，记录所有处理的附件ID

使用方法：
1. 启动Odoo shell: python odoo-bin shell -d your_database
2. 在shell中运行: exec(open('myaddons/hangxing_release/ylhc_synology_integration/test_unified_sync.py').read())
"""

def test_unified_sync_logic():
    """测试统一同步日志逻辑"""
    print("=== 测试统一同步日志逻辑 ===")
    
    # 获取模型
    sync_log_model = env['synology.sync.log']
    attachment_model = env['ir.attachment']
    config_model = env['synology.attachment.config']
    
    # 1. 查看当前配置
    print("\n1. 查看当前同步配置...")
    
    configs = config_model.search([
        ('active', '=', True),
        ('upload_to_synology', '=', True)
    ])
    
    print(f"   找到 {len(configs)} 个活跃配置:")
    total_fields = 0
    for config in configs:
        active_fields = config.field_ids.filtered('active')
        total_fields += len(active_fields)
        print(f"   - {config.model_name}: {len(active_fields)} 个字段")
        for field in active_fields:
            print(f"     * {field.field_name} (sync_existing: {field.sync_existing})")
    
    print(f"   总共 {total_fields} 个字段配置")
    
    # 2. 运行测试同步
    print(f"\n2. 运行测试同步...")
    
    # 记录同步前的日志数量
    before_count = sync_log_model.search_count([])
    print(f"   同步前日志数量: {before_count}")
    
    try:
        # 运行测试模式的同步
        result = attachment_model.with_context(
            test_mode=True,
            max_attachments=5
        ).cron_sync_attachments_to_synology()
        
        print("   ✓ 测试同步完成")
        
        # 记录同步后的日志数量
        after_count = sync_log_model.search_count([])
        new_logs_count = after_count - before_count
        
        print(f"   同步后日志数量: {after_count}")
        print(f"   新增日志数量: {new_logs_count}")
        
        # 3. 分析新创建的日志
        print(f"\n3. 分析新创建的同步日志...")
        
        new_logs = sync_log_model.search([
            ('sync_type', '=', 'test')
        ], limit=new_logs_count, order='create_date desc')
        
        print(f"   预期: 1个统一日志 (不管有多少字段配置)")
        print(f"   实际: {len(new_logs)} 个日志")
        
        if len(new_logs) == 1:
            print("   ✓ 正确：只创建了一个统一的同步日志")
            log = new_logs[0]
            
            print(f"\n   日志详情:")
            print(f"   - ID: {log.id}")
            print(f"   - 模型: {log.model_name}")
            print(f"   - 字段: {log.field_name or '(统一处理)'}")
            print(f"   - 同步时间: {log.sync_date}")
            print(f"   - 处理数量: {log.total_count}")
            print(f"   - 成功数量: {log.success_count}")
            print(f"   - 失败数量: {log.failed_count}")
            print(f"   - 状态: {log.status}")
            
            # 查看附件ID
            attachment_ids = log.get_attachment_ids_list()
            print(f"   - 处理的附件ID: {attachment_ids}")
            
            # 查看备注
            if log.notes:
                print(f"   - 备注:")
                for line in log.notes.split('\n'):
                    if line.strip():
                        print(f"     {line}")
        
        elif len(new_logs) > 1:
            print(f"   ⚠ 警告：创建了 {len(new_logs)} 个日志，应该只有1个")
            for i, log in enumerate(new_logs):
                print(f"   日志 {i+1}: {log.model_name}.{log.field_name} - {log.total_count} 个附件")
        
        else:
            print("   ✗ 错误：没有创建同步日志")
        
        return new_logs
        
    except Exception as e:
        print(f"   ✗ 测试同步失败: {str(e)}")
        return []

def test_attachment_filtering():
    """测试附件过滤逻辑"""
    print("\n=== 测试附件过滤逻辑 ===")
    
    sync_log_model = env['synology.sync.log']
    attachment_model = env['ir.attachment']
    
    # 查找一个有历史同步记录的模型+字段组合
    recent_log = sync_log_model.search([
        ('model_name', '!=', False),
        ('field_name', '!=', False),
        ('sync_type', 'in', ['scheduled', 'manual', 'api']),
        ('status', 'in', ['success', 'partial'])
    ], limit=1, order='sync_date desc')
    
    if recent_log:
        print(f"   使用历史日志: {recent_log.model_name}.{recent_log.field_name}")
        print(f"   上次同步时间: {recent_log.sync_date}")
        
        # 查询在该时间之后创建的附件
        domain = [
            ('res_model', '=', recent_log.model_name),
            ('res_field', '=', recent_log.field_name),
            ('type', '=', 'binary'),
            '|',
            ('synology_sync_status', '=', False),
            ('synology_sync_status', 'in', ['pending', 'failed']),
            ('create_date', '>', recent_log.sync_date)
        ]
        
        new_attachments = attachment_model.search(domain, limit=10)
        print(f"   找到 {len(new_attachments)} 个需要同步的新附件")
        
        # 对比：不限制时间的查询
        all_domain = domain[:-1]  # 移除时间限制
        all_attachments = attachment_model.search(all_domain, limit=100)
        print(f"   总共有 {len(all_attachments)} 个符合条件的附件")
        print(f"   增量同步节省了 {len(all_attachments) - len(new_attachments)} 个附件的处理")
        
    else:
        print("   没有找到历史同步记录，无法测试过滤逻辑")

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    sync_log_model = env['synology.sync.log']
    
    # 删除测试日志
    test_logs = sync_log_model.search([('sync_type', '=', 'test')])
    count = len(test_logs)
    
    if count > 0:
        test_logs.unlink()
        print(f"   已删除 {count} 个测试同步日志")
    else:
        print("   没有找到测试同步日志")

def main():
    """主测试函数"""
    if 'env' not in globals():
        print("请在Odoo shell中运行此脚本")
        print("启动命令: python odoo-bin shell -d your_database")
        return
    
    print("开始测试统一同步日志逻辑...")
    
    # 1. 测试统一同步逻辑
    new_logs = test_unified_sync_logic()
    
    # 2. 测试附件过滤逻辑
    test_attachment_filtering()
    
    print("\n=== 测试总结 ===")
    print("统一同步日志的优势:")
    print("1. 一次同步任务只创建一个日志记录")
    print("2. 记录所有处理的附件ID，便于追踪")
    print("3. 简化日志管理，避免日志碎片化")
    print("4. 基于上次同步时间的增量处理")
    print("5. 统一的错误处理和状态跟踪")
    
    # 询问是否清理测试数据
    print("\n是否要清理测试数据？")
    print("取消注释下面的行来清理:")
    print("# cleanup_test_data()")
    
    return new_logs

if __name__ == '__main__':
    main()
