# 群晖附件集成模块

## 概述
此模块通过拦截 `ir.attachment` 的创建过程，自动将附件上传到群晖NAS并转换为URL类型。

## 核心特性
- **自动上传**：创建附件时自动检测并上传到群晖
- **配置驱动**：通过配置表控制哪些模型使用群晖存储
- **完全后端**：无需前端代码，所有逻辑在后端处理
- **透明集成**：开发者无需修改现有代码

## 工作原理
```python
# 任何地方创建附件
attachment = self.env['ir.attachment'].create({
    'name': 'document.pdf',
    'type': 'binary',
    'datas': file_data,
    'res_model': 'purchase.order',  # 自动检查此模型的配置
    'res_id': 123,
})

# 如果purchase.order配置了群晖上传
# attachment.type 会变成 'url'
# attachment.url 包含群晖分享链接
```

## 配置

### 1. 群晖连接设置
进入 `设置 > 技术 > 系统参数` 配置：
- `ylhc_synology_integration.ip_address`：群晖IP地址
- `ylhc_synology_integration.port`：端口（通常5000或5001）
- `ylhc_synology_integration.username`：用户名
- `ylhc_synology_integration.password`：密码
- `ylhc_synology_integration.destination_path`：目标文件夹路径

### 2. 模型配置
进入 `设置 > 技术 > 群晖附件配置`：
- 选择需要使用群晖的模型
- 设置文件大小限制、允许的扩展名等

## 使用示例

### 基本使用
```python
# 创建附件会自动处理
attachment = self.env['ir.attachment'].create({
    'name': 'contract.pdf',
    'type': 'binary',
    'datas': base64_encoded_data,
    'res_model': 'sale.order',
    'res_id': self.id,
})
```

### 向导中使用
```python
class UploadWizard(models.TransientModel):
    _name = 'upload.wizard'
    
    file = fields.Binary()
    filename = fields.Char()
    
    def action_upload(self):
        # 直接创建附件，自动处理群晖上传
        attachment = self.env['ir.attachment'].create({
            'name': self.filename,
            'type': 'binary',
            'datas': self.file,
            'res_model': 'target.model',
            'res_id': target_id,
        })
        
        # 检查是否上传成功
        if attachment.type == 'url':
            # 已上传到群晖
            url = attachment.url
        else:
            # 保存在本地
            pass
```

## 同步日志功能

模块包含完整的同步日志系统，用于跟踪和监控同步过程：

### 记录的信息
- **同步时间**：同步发生的具体时间
- **同步类型**：手动、定时、API或测试同步
- **记录ID**：处理的记录标识
- **附件ID**：同步的附件标识
- **成功/失败统计**：详细的统计信息
- **文件大小**：传输的数据总量
- **持续时间**：同步耗时
- **错误详情**：失败同步的具体错误信息
- **成功详情**：成功同步的群晖路径和URL

### 查看同步日志
1. 进入 **群晖集成 > 同步日志**
2. 查看每次同步操作的详细信息
3. 按日期、状态、同步类型或模型筛选
4. 点击任何日志条目查看详细信息
5. 使用"查看附件"按钮查看相关文件

### 自动清理
- 30天后自动清理旧的同步日志
- 可通过定时任务"清理旧的群晖同步日志"进行配置

### 监控和故障排除
- **成功率**：监控同步成功的百分比
- **错误详情**：查看失败同步的具体错误信息
- **性能跟踪**：监控同步持续时间和文件大小
- **趋势分析**：使用筛选和分组分析同步模式

### 手动同步与日志
可以手动同步附件并跟踪过程：
1. 在附件列表中选择附件
2. 使用"批量同步到群晖"操作
3. 检查同步日志查看详细结果

## 定时任务管理

模块提供了完整的定时任务系统来自动同步附件：

### 预设的定时任务
1. **每日同步** (默认启用)
   - 每天凌晨2点运行
   - 适合大多数使用场景
   - 平衡性能和及时性

2. **每小时同步** (默认禁用)
   - 每小时运行一次
   - 适合高频率文件上传的环境
   - 需要更多系统资源

3. **每周同步** (默认禁用)
   - 每周运行一次
   - 适合低频率使用的环境
   - 节省系统资源

4. **手动测试同步** (默认禁用)
   - 用于测试和调试
   - 限制处理数量，避免影响生产环境

### 定时任务管理界面
进入 **群晖集成 > 定时任务** 可以：
- 查看所有群晖相关的定时任务
- 启用/禁用特定任务
- 调整执行时间和频率
- 立即运行任务进行测试

### 快速操作
进入 **群晖集成 > 快速操作** 可以：
- **启用每日同步**：启用每日同步，禁用其他同步
- **启用每小时同步**：启用每小时同步，禁用其他同步
- **禁用所有同步**：禁用所有自动同步任务

### 定时任务特性
- **智能日志记录**：每次运行都会创建详细的同步日志
- **增量同步**：只处理上次同步之后新增的附件记录
- **时间跟踪**：基于同步日志的执行时间确定处理范围
- **测试模式支持**：可以限制处理数量进行安全测试
- **智能文件名**：自动处理文件名，确保群晖正确识别文件类型
- **错误处理**：完整的异常处理和错误记录
- **性能优化**：批量处理，避免系统过载和重复处理
- **灵活配置**：支持不同频率的同步需求

### 增量同步机制
系统实现了智能的增量同步机制：

1. **首次同步**：如果没有同步历史，处理2025-08-02 18:00:00之后的所有附件
2. **后续同步**：只处理上次成功同步时间之后新增的附件
3. **时间基准**：使用同步日志的`sync_date`（同步执行时间）作为时间基准
4. **状态过滤**：只考虑状态为'success'或'partial'的同步日志
5. **字段级别**：每个模型的每个字段都有独立的增量跟踪

**示例**：
- 上次同步时间：2025-08-02 20:00:00
- 当前同步只会处理在20:00:00之后创建的附件
- 避免重复处理已同步的历史附件

## 智能文件名处理

系统提供智能的文件名处理机制，确保上传到群晖的文件有正确的文件名和扩展名：

### 文件名处理逻辑
1. **检测字段名文件**：如果附件的文件名等于二进制字段名（如"datas"），系统会尝试获取更好的文件名
2. **从源记录获取**：查找配置的文件名字段，从源记录获取真实的文件名
3. **随机文件名生成**：如果无法获取文件名，生成带正确扩展名的随机文件名
4. **文件名清理**：移除非法字符，确保文件名在群晖系统中有效
5. **扩展名识别**：根据mimetype自动确定正确的文件扩展名
6. **同步更新**：自动更新Odoo附件记录的名称，保持与群晖一致

### 配置文件名字段
在字段配置中可以指定文件名字段：
- 进入 **群晖集成 > 附件配置**
- 编辑字段映射
- 设置 **文件名字段**（通常是 `字段名_file_name`）
- 系统会自动从该字段获取文件名

### 扩展名映射
系统根据mimetype自动确定扩展名：
- `application/pdf` → `.pdf`
- `image/jpeg` → `.jpg`
- `image/png` → `.png`
- `application/vnd.openxmlformats-officedocument.wordprocessingml.document` → `.docx`
- `application/msword` → `.doc`
- `text/plain` → `.txt`
- 其他类型 → `.bin`

### 文件名示例
| 场景 | 原始文件名 | 处理结果 | Odoo记录更新 |
|------|------------|----------|--------------|
| 正常文件名 | `采购合同.pdf` | `采购合同.pdf` | 无需更新 |
| 字段名文件 | `datas` | `采购订单_PO001.pdf` | ✓ 更新为新名称 |
| 包含非法字符 | `文件/名*.pdf` | `文件_名_.pdf` | ✓ 更新为清理后名称 |
| 无扩展名 | `document` | `document.pdf` | ✓ 更新为带扩展名 |
| 随机生成 | `datas` | `order_123_a1b2c3d4.pdf` | ✓ 更新为随机名称 |

### 同步更新机制
- **自动检测**：系统自动检测文件名是否需要改进
- **同步更新**：当生成更好的文件名时，同时更新Odoo附件记录
- **一致性保证**：确保Odoo中显示的文件名与群晖上的文件名完全一致
- **日志记录**：文件名更新过程会记录在日志中，便于追踪

## 依赖
- Python包：`synology-api`
- Odoo模块：`base`, `mail`

## 版本
16.0.2.0.1