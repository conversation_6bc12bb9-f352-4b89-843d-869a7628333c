#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能文件名功能的脚本
验证文件名处理逻辑：字段名 -> 源记录文件名 -> 随机文件名

使用方法：
1. 启动Odoo shell: python odoo-bin shell -d your_database
2. 在shell中运行: exec(open('myaddons/hangxing_release/ylhc_synology_integration/test_smart_filename.py').read())
"""

def test_smart_filename():
    """测试智能文件名功能"""
    print("=== 测试智能文件名功能 ===")

    # 获取模型
    attachment_model = env['ir.attachment']

    # 创建一个测试附件实例
    test_attachment = attachment_model.new({
        'name': 'datas',  # 模拟文件名就是字段名的情况
        'res_model': 'purchase.order',
        'res_id': 1,
        'res_field': 'datas',
        'type': 'binary',
        'mimetype': 'application/pdf'
    })
    
    print("\n1. 测试场景1：文件名等于字段名")
    print(f"   原始文件名: {test_attachment.name}")
    print(f"   字段名: {test_attachment.res_field}")
    print(f"   文件名 == 字段名: {test_attachment.name == test_attachment.res_field}")
    
    # 测试智能文件名生成
    smart_filename = test_attachment._get_smart_filename(
        test_attachment.name,
        test_attachment.res_model,
        test_attachment.res_id,
        test_attachment.res_field
    )
    
    print(f"   智能文件名: {smart_filename}")
    
    # 2. 测试场景2：正常文件名
    print("\n2. 测试场景2：正常文件名")
    
    normal_filename = test_attachment._get_smart_filename(
        "采购订单.pdf",
        "purchase.order",
        1,
        "datas"
    )
    
    print(f"   原始文件名: 采购订单.pdf")
    print(f"   处理后文件名: {normal_filename}")
    
    # 3. 测试场景3：包含非法字符的文件名
    print("\n3. 测试场景3：包含非法字符的文件名")
    
    illegal_filename = test_attachment._get_smart_filename(
        "文件/名\\包含:非法*字符?.pdf",
        "purchase.order",
        1,
        "datas"
    )
    
    print(f"   原始文件名: 文件/名\\包含:非法*字符?.pdf")
    print(f"   清理后文件名: {illegal_filename}")
    
    # 4. 测试随机文件名生成
    print("\n4. 测试随机文件名生成")
    
    for mimetype, expected_ext in [
        ('application/pdf', '.pdf'),
        ('image/jpeg', '.jpg'),
        ('image/png', '.png'),
        ('application/vnd.openxmlformats-officedocument.wordprocessingml.document', '.docx'),
        ('application/msword', '.doc'),
        ('text/plain', '.txt'),
        ('application/octet-stream', '.bin')
    ]:
        test_attachment.mimetype = mimetype
        random_filename = test_attachment._generate_random_filename(
            'purchase.order',
            'datas',
            123
        )
        print(f"   {mimetype} -> {random_filename}")
        
        # 验证扩展名
        if random_filename.endswith(expected_ext):
            print(f"     ✓ 正确的扩展名: {expected_ext}")
        else:
            print(f"     ⚠ 扩展名不匹配，期望: {expected_ext}")

def test_filename_from_record():
    """测试从源记录获取文件名"""
    print("\n=== 测试从源记录获取文件名 ===")
    
    # 查找一个实际的采购订单记录
    purchase_model = env['purchase.order']
    purchase_orders = purchase_model.search([], limit=3)
    
    if not purchase_orders:
        print("   没有找到采购订单记录，无法测试")
        return
    
    print(f"   找到 {len(purchase_orders)} 个采购订单记录")
    
    # 查看字段配置
    config_model = env['synology.attachment.config.field']
    field_configs = config_model.search([
        ('config_id.model_name', '=', 'purchase.order'),
        ('field_name', '=', 'datas'),
        ('active', '=', True)
    ])
    
    if field_configs:
        field_config = field_configs[0]
        print(f"   找到字段配置: {field_config.field_name}")
        print(f"   文件名字段: {field_config.filename_field or '未配置'}")
        
        # 测试从记录获取文件名
        for po in purchase_orders:
            print(f"\n   测试采购订单 {po.id}: {po.name}")
            
            # 检查是否有文件名字段
            if field_config.filename_field and hasattr(po, field_config.filename_field):
                filename_value = getattr(po, field_config.filename_field)
                print(f"     文件名字段值: {filename_value or '空'}")
            else:
                print(f"     没有文件名字段或字段不存在")
            
            # 模拟智能文件名生成
            attachment_model = env['ir.attachment']
            test_attachment = attachment_model.new({
                'name': 'datas',
                'res_model': 'purchase.order',
                'res_id': po.id,
                'res_field': 'datas',
                'mimetype': 'application/pdf'
            })
            
            smart_filename = test_attachment._get_smart_filename(
                'datas',  # 文件名等于字段名
                'purchase.order',
                po.id,
                'datas'
            )
            
            print(f"     智能文件名: {smart_filename}")
    
    else:
        print("   没有找到purchase.order的datas字段配置")

def test_filename_cleaning():
    """测试文件名清理功能"""
    print("\n=== 测试文件名清理功能 ===")
    
    attachment_model = env['ir.attachment']
    test_attachment = attachment_model.new({})
    
    test_cases = [
        ("正常文件名.pdf", "正常文件名.pdf"),
        ("文件/名.pdf", "文件_名.pdf"),
        ("文件\\名.pdf", "文件_名.pdf"),
        ("文件:名.pdf", "文件_名.pdf"),
        ("文件?名.pdf", "文件_名.pdf"),
        ("文件*名.pdf", "文件_名.pdf"),
        ("文件<名>.pdf", "文件_名_.pdf"),
        ("文件|名.pdf", "文件_名.pdf"),
        ('文件"名.pdf', "文件_名.pdf"),
        ("  .文件名.  ", "文件名"),
        ("", None),
        (None, None)
    ]
    
    print("   文件名清理测试:")
    for original, expected in test_cases:
        cleaned = test_attachment._clean_filename(original)
        status = "✓" if cleaned == expected else "✗"
        print(f"   {status} '{original}' -> '{cleaned}' (期望: '{expected}')")

def test_attachment_name_sync():
    """测试附件名称同步更新功能"""
    print("\n=== 测试附件名称同步更新功能 ===")

    attachment_model = env['ir.attachment']

    # 创建一个真实的测试附件
    test_attachment = attachment_model.create({
        'name': 'datas',  # 文件名等于字段名
        'res_model': 'purchase.order',
        'res_id': 1,
        'res_field': 'datas',
        'type': 'binary',
        'mimetype': 'application/pdf',
        'datas': 'JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKPD4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovTWVkaWFCb3ggWzAgMCA2MTIgNzkyXQo+PgplbmRvYmoKeHJlZgowIDQKMDAwMDAwMDAwMCA2NTUzNSBmIAowMDAwMDAwMDA5IDAwMDAwIG4gCjAwMDAwMDAwNTggMDAwMDAgbiAKMDAwMDAwMDExNSAwMDAwMCBuIAp0cmFpbGVyCjw8Ci9TaXplIDQKL1Jvb3QgMSAwIFIKPj4Kc3RhcnR4cmVmCjE3NAolJUVPRgo='  # 简单的PDF内容
    })

    print(f"   创建测试附件: ID={test_attachment.id}")
    print(f"   初始文件名: '{test_attachment.name}'")
    print(f"   字段名: '{test_attachment.res_field}'")
    print(f"   文件名 == 字段名: {test_attachment.name == test_attachment.res_field}")

    # 模拟智能文件名处理
    original_name = test_attachment.name
    smart_filename = test_attachment._get_smart_filename(
        test_attachment.name,
        test_attachment.res_model,
        test_attachment.res_id,
        test_attachment.res_field
    )

    print(f"   智能文件名: '{smart_filename}'")

    # 模拟上传过程中的名称更新
    if smart_filename != original_name:
        print(f"   文件名需要更新: '{original_name}' -> '{smart_filename}'")
        test_attachment.write({'name': smart_filename})

        # 验证更新结果
        test_attachment.refresh()
        print(f"   更新后的附件名: '{test_attachment.name}'")

        if test_attachment.name == smart_filename:
            print("   ✓ 附件名称同步更新成功")
        else:
            print("   ✗ 附件名称同步更新失败")
    else:
        print("   文件名无需更新")

    # 清理测试数据
    test_attachment.unlink()
    print(f"   已清理测试附件")

def test_real_sync_with_name_update():
    """测试真实同步过程中的名称更新"""
    print("\n=== 测试真实同步过程中的名称更新 ===")

    # 查找一个实际的附件进行测试
    attachment_model = env['ir.attachment']

    # 查找文件名等于字段名的附件
    field_name_attachments = attachment_model.search([
        ('type', '=', 'binary'),
        ('res_field', '!=', False),
        ('name', '=', 'datas')  # 文件名等于常见字段名
    ], limit=3)

    if field_name_attachments:
        print(f"   找到 {len(field_name_attachments)} 个文件名等于字段名的附件")

        for att in field_name_attachments:
            print(f"\n   附件 {att.id}:")
            print(f"     当前名称: '{att.name}'")
            print(f"     字段名: '{att.res_field}'")
            print(f"     模型: {att.res_model}")
            print(f"     记录ID: {att.res_id}")

            # 生成智能文件名
            smart_filename = att._get_smart_filename(
                att.name,
                att.res_model,
                att.res_id,
                att.res_field
            )

            print(f"     智能文件名: '{smart_filename}'")

            if smart_filename != att.name:
                print(f"     建议更新: '{att.name}' -> '{smart_filename}'")
                print("     (这是模拟，实际同步时会自动更新)")
            else:
                print("     无需更新")
    else:
        print("   没有找到文件名等于字段名的附件")

        # 创建一个示例来演示
        print("   创建示例附件进行演示...")
        demo_attachment = attachment_model.create({
            'name': 'datas',
            'res_model': 'res.partner',
            'res_id': 1,
            'res_field': 'datas',
            'type': 'binary',
            'mimetype': 'application/pdf',
            'datas': 'JVBERi0='  # 最小PDF
        })

        print(f"   创建演示附件: ID={demo_attachment.id}, 名称='{demo_attachment.name}'")

        # 演示智能文件名
        smart_filename = demo_attachment._get_smart_filename(
            demo_attachment.name,
            demo_attachment.res_model,
            demo_attachment.res_id,
            demo_attachment.res_field
        )

        print(f"   智能文件名: '{smart_filename}'")

        # 清理
        demo_attachment.unlink()
        print("   已清理演示附件")

def main():
    """主测试函数"""
    if 'env' not in globals():
        print("请在Odoo shell中运行此脚本")
        print("启动命令: python odoo-bin shell -d your_database")
        return
    
    print("开始测试智能文件名功能...")
    
    # 1. 测试智能文件名
    test_smart_filename()
    
    # 2. 测试从记录获取文件名
    test_filename_from_record()
    
    # 3. 测试文件名清理
    test_filename_cleaning()

    # 4. 测试附件名称同步更新
    test_attachment_name_sync()

    # 5. 测试真实同步过程
    test_real_sync_with_name_update()

    print("\n=== 测试总结 ===")
    print("智能文件名功能特点:")
    print("1. 检测文件名是否等于字段名")
    print("2. 从源记录的文件名字段获取真实文件名")
    print("3. 如果无法获取，生成带正确扩展名的随机文件名")
    print("4. 清理文件名中的非法字符")
    print("5. 根据mimetype自动确定文件扩展名")
    print("6. 确保群晖能正确识别文件类型")
    print("7. 同步更新Odoo附件记录的名称，保持一致性")

if __name__ == '__main__':
    main()
