<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="view_touchan_filter" model="ir.ui.view">
            <field name="name">touchan.select</field>
            <field name="model">purchase.requisition</field>
            <field name="arch" type="xml">
                <search string="投产单选择">
                    <field name="name" string="投产单名称" filter_domain="['|', ('name', 'ilike', self), ('origin', 'ilike', self)]"/>
                    <field name="user_id"/>
                    <field name="touchan_ren"/>
                    <field name="product_id"/>
                    <filter string="需要我采购的投产单" name="my_agreements" domain="[('user_id', '=', uid)]"/>
                    <filter string="我提出的投产单" name="my_touchan" domain="[('touchan_ren', '=', uid)]"/>
                    <separator/>
                    <filter string="草稿" name="draft" domain="[('state', '=', 'draft')]" help="New Agreements"/>
                    <filter string="已确认" name="confirmed" domain="[('state', 'in', ('in_progress', 'open'))]" help="In negotiation"/>
                    <filter string="完成" name="done" domain="[('state', '=', 'done')]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="投产人" name="touchanren2" domain="[]" context="{'group_by': 'touchan_ren'}"/>
                        <filter string="采购员" name="representative" domain="[]" context="{'group_by': 'user_id'}"/>
                        <filter string="状态" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="希望到货日期" name="schedule_date" domain="[]" context="{'group_by': 'schedule_date'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="view_purchase_requisition_form_custom" model="ir.ui.view">
            <field name="name">view_purchase_requisition_form_custom</field>
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_form"/>
            <field name="priority" eval="50"/>
            <field name="arch" type="xml">
                <xpath expr="//header/button[@name='action_cancel']" position="after">
                    <button name="update_demand"  string="更新需求数量" type="object" class="btn-primary"/>
                    <button name="action_supplier_select"  string="选择供应商" type="object" class="btn-primary" 
                        attrs="{'invisible': ['|',('state', '!=', 'open'),'|',('purchase_product_type', '!=', 'jiegoujian'),('supplier_select_count', '!=', 0)]}" />
                </xpath>
                <xpath expr="//button[@name='%(purchase_requisition.action_purchase_requisition_list)d']" position="before">
                    <field name="supplier_select_count" invisible="1"/>
                    <button name="action_supplier_select_result" type="object" class="oe_stat_button" icon="fa-list-alt" string="供应商选择结果"
                        attrs="{'invisible': [('supplier_select_count', '=', 0)]}">
                    </button>
                </xpath>
                <xpath expr="//field[@name='ordering_date']" position="attributes">
                     <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='currency_id']" position="after">
                     <field name="reason" required="1"/>
                     <field name="purchase_type"/>
                </xpath>
                <xpath expr="//header/button[@name='%(purchase_requisition.action_purchase_requisition_to_so)d']" position="attributes">
                    <attribute name="attrs">
                        {'invisible': ['|',('state', '!=', 'open'),('supplier_select_count', '!=', 0)]}
                    </attribute>
                </xpath>
                <xpath expr="//group/field[@name='user_id']" position="replace">
                     <field name="user_id" attrs="{'readonly': [('state','not in',('draft','in_progress','open'))]}" domain="[('share', '=', False),('category_ids.name', '=', ('采购员'))]"/>
                </xpath>
                <xpath expr="//group/field[@name='schedule_date']" position="replace">
                     <field name="schedule_date" 
                     attrs="{'readonly': [('state','not in',('draft','in_progress','open','ongoing'))]}" 
                     required ='1'
                     string="希望到货日期"/>
                </xpath>
                <xpath expr="//group/field[@name='vendor_id']" position="after">
                     <field name="project_id" />
                     <field name="is_have_project_batch" invisible="1" />
                     <field name="project_batch_id" domain="[('project_id', '=', project_id)]" attrs="{'invisible': [('is_have_project_batch', '=', False)]}" options= "{'no_create': True}" />
                     <field name="purchase_product_type" invisible="1" readonly="1"/>
                     <!-- <field name="approval_id" /> -->
                     <field name="applicant_id" />
                </xpath>
                <xpath expr="//sheet/group/group[2]/field[@name='company_id']" position="after">
                     <field name="is_bom_based"/>
                     <label for="bom_id" attrs="{'invisible': [('is_bom_based', '=', False)], 'required': [('is_bom_based', '=', True)]}"/>
                     <div>
                         <field name="bom_id" attrs="{'invisible': [('is_bom_based', '=', False)], 'required': [('is_bom_based', '=', True)]}" style="width:70%"
                                options="{'no_create': True, 'no_create_edit': True, 'tree_view_ref': 'custom_purchase_requisition.mrp_bom_tree_view_simple', 'search_view_ref': 'custom_purchase_requisition.view_mrp_bom_filter_simple'}"/>
                         <button name="action_bom_line_select" type="object" string="选择BOM清单"
                            attrs="{'invisible': [('is_bom_based', '=', False)]}" style="display:inline;"/>
                     </div>
                     <field name="production_quantity"/>
                </xpath>

                <xpath expr="//page[@name='products']/field[@name='line_ids']/tree/field[@name='product_id']" position="after">
                     <field name="product_text_all" />
                </xpath>
                <xpath expr="//page[@name='products']/field[@name='line_ids']/tree/field[@name='product_description_variants']" position="replace">
                    <field name="product_description_variants" 
                        attrs="{'invisible': [('product_description_variants', '=', '')], 'readonly': [('parent.state', '!=', 'draft')]}" optional="hide"/>
                </xpath>
                <xpath expr="//page[@name='products']/field[@name='line_ids']/tree/field[@name='analytic_distribution']" position="replace">
                    <field name="demand_qty"/>
                    <field name="depletion_qty"/>
                    <field name="warehouse_in_hand" />
                    <field name="in_transit" />
                    <field name="pending_production"/>
                    <field name="already_occupied"/>
                    <field name="already_occupied_by_others"/>
                    <field name="available_qty" />
                    <field name="purchase_qty" />
                    <field name="analytic_distribution" widget="analytic_distribution"
                        groups="analytic.group_analytic_accounting"
                        options="{'product_field': 'product_id', 'business_domain': 'purchase_order'}"
                        string="项目"/>
                </xpath>
                <xpath expr="//page[@name='products']/field[@name='line_ids']/tree/field[@name='schedule_date']" position="replace">
                    <field name="schedule_date" string="希望日期" />
                </xpath>
                <xpath expr="//page[@name='products']/field[@name='line_ids']/tree/field[@name='schedule_date']" position="after">
                    <field name="company_id" invisible="1"/>
                    <field name="vendor_id" />
                </xpath>
                <!-- <xpath expr="//page[@name='products']" position="after">
                    <page string="附件(需要摆渡资料)">
                        <group col="1">
                            <field name="attachment_ids" widget="many2many_binary" width="50%" />
                        </group>
                    </page>
                </xpath> -->
                <xpath expr="//notebook" position="before">
                    <div class="p-2">
                        <b>采购负责范围说明：</b> 
                        <br/>
                        杨旭峰：担任采购主管，负责丝杠、锥/齿轮、谐波、PCB电装、外协实验、传感器、PCB板、标准件、电机、辅料采购及供应商管理等工作;
                        <br/>
                        白馥境：担任采购主管,负责办公用品、家具、工具、设备、电气类元器件和连接器采购及供应商管理等工作。
                        <br/>
                        孙元辉：机加件
                        <br/>
                    </div>
                </xpath>
                <xpath expr="//page[@name='products']/field[@name='line_ids']/tree/field[@name='analytic_distribution']" position="attributes">
                    <attribute name="required">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_purchase_requisition_tree_custom" model="ir.ui.view">
            <field name="name">view_purchase_requisition_tree_custom</field>
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="before">
                    <field name="project_id" optional="show"/>
                    <field name="project_batch_id" optional="show"/>
                    <field name="purchase_ids" widget="many2many_tags"/>
                </xpath>
                <xpath expr="//field[@name='company_id']" position="replace">
                    <field name="applicant_id" optional="show" widget='many2one_avatar_user' string="采购申请人"/>       
                    <field name="create_uid" optional="show" widget='many2one_avatar_user' string="计划单创建者"/>
                    <field name="create_date" widget='remaining_days' string="计划单创建日期"/>
                </xpath>
                <xpath expr="//field[@name='schedule_date']" position="replace">
                    <field name="order_count" string="已创建采购订单数" />
                    <field name="total_product_quantity" />
                    <field name="total_ordered_quantity" />
                    <field name="total_qty_ordered_percentage" widget="percentage"/>
                    <field name="schedule_date" string="希望到货日期"/>
                </xpath>
            </field>
        </record>

        <record id="view_purchase_requisition_filter_custom" model="ir.ui.view">
            <field name="name">iew_purchase_requisition_filter_custom</field>
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='my_agreements']" position="replace">
                    <filter string="需要我采购的计划单" name="my_agreements" domain="[('user_id', '=', uid)]"/>
                    <separator/>
                     <filter string="我提出的采购计划单" name="my_purchase_requisition" domain="['|',('create_uid', '=', uid),('applicant_id', '=', uid)]"/>
                </xpath>
                <xpath expr="//filter[@name='done']" position="after">
                    <separator/>
                    <filter string="已创建采购单" name="createdOrder" domain="[('purchase_ids', '!=', False)]"/>
                    <filter string="未创建采购单" name="notCreatedOrder" domain="[('purchase_ids', '=', False)]"/>
                </xpath>
               
            </field>
        </record>

        <record model="ir.ui.view" id="view_touchan_form">
            <field name="name">purchase.requisition.form</field>
            <field name="model">purchase.requisition</field>
            <field name="arch" type="xml">
                <form string="Purchase Agreements">
                <field name="company_id" invisible="1"/>
                <header>
                    <button name="%(purchase_requisition.action_purchase_requisition_to_so)d" type="action"
                        string="新的报价"
                        attrs="{'invisible': [('state', '!=', 'open')]}"/>
                    <button name="%(purchase_requisition.action_purchase_requisition_to_so)d" type="action"
                        string="新的报价" class="btn-primary"
                        attrs="{'invisible': [('state', 'not in', ('in_progress', 'ongoing'))]}"/>
                    <button name="action_in_progress" states="draft" string="确认" type="object" class="btn-primary"/>
                    <button name="action_open" states="in_progress" string="验证" type="object" class="btn-primary"/>
                    <button name="action_done" states="open,ongoing" string="关闭" type="object" class="btn-primary"/>
                    <button name="action_draft" states="cancel" string="返回草稿" type="object"/>
                    <button name="action_cancel" states="draft,in_progress,ongoing" string="取消" type="object"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,open,done" attrs="{'invisible': [('is_quantity_copy', '=', 'none')]}"/>
                    <field name="state_blanket_order" widget="statusbar" statusbar_visible="draft,ongoing,done" attrs="{'invisible': [('is_quantity_copy', '!=', 'none')]}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(purchase_requisition.action_purchase_requisition_list)d" type="action" class="oe_stat_button" icon="fa-list-alt"
                            attrs="{'invisible': [('state', '=', 'draft')]}">
                            <field name="order_count" widget="statinfo" string="RFQs/订单"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_inline"/>
                        <h1 class="d-flex">
                            <field name="priority" widget="priority" class="me-3"/>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="is_quantity_copy" invisible='1'/>
                            <field name="order_type" context="{'default_order_type': 'touchandan'}" invisible='1'/>
                            <field name="user_id" attrs="{'readonly': [('state','not in',('draft','in_progress','open'))]}" domain="[('share', '=', False),('category_ids.name', '=', ('采购员'))]"/>
                            <field name="type_id" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="vendor_id" context="{'res_partner_search_mode': 'supplier'}" attrs="{'required': [('is_quantity_copy', '=', 'none')], 'readonly': [('state', 'in', ['ongoing','done'])]}"/>
                            <field name="project_id" attrs="{'required': [('order_type', '=', 'touchandan')],'readonly': [('state','!=','draft')]}"/>
                            <field name="touchan_ren" attrs="{'required': [('order_type', '=', 'touchandan')],'readonly': [('state','not in',('draft'))]}"/>
                            <field name="touchan_product"  attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="touchan_cnt"   attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="currency_id" groups="base.group_multi_currency"/>
                            <field name="reason"/>
                            <field name="purchase_type"/>
                        </group>
                        <group>
                            <field name="date_end" attrs="{'readonly': [('state','not in',('draft','in_progress','open','ongoing'))]}"/>
                            <field name="ordering_date" attrs="{'readonly': [('state','not in',('draft','in_progress','open','ongoing'))]}"/>
                            <field name="schedule_date" string="希望到货日期" attrs="{'readonly': [('state','not in',('draft','in_progress','open','ongoing'))]}"/>
                            <field name="origin" placeholder="e.g. PO0025" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="gongyi_ren"  attrs="{'required': [('order_type', '=', 'touchandan')],'readonly': [('state','not in',('draft'))]}"/>
                            <field name="jishuwenjian"  attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" attrs="{'readonly': [('state','not in',('draft'))]}"/>
                        </group>
                    </group>
                    <group>
                        <group colspan="2">
                            <field name="production_file_place" attrs="{'readonly': [('state','not in',('draft'))]}"/>
                        </group>
                        <group colspan="2">
                            <field name="archives_url" widget="url" attrs="{'readonly': [('state','not in',('draft'))]}"/>
                        </group>
                        <group colspan="2">
                            <field name="touchan_suoming" widget="text" attrs="{'readonly': [('state','not in',('draft'))]}" /> 
                        </group>
                        <!-- <group colspan="2">
                            <field name="xiangmushengpi_suoming" widget="text" attrs="{'readonly': [('state','not in',('draft'))]}" /> 
                        </group> -->
                        <group colspan="2">
                            <field name="yugu_gongqi" widget="text" attrs="{'readonly': [('state','not in',('draft'))]}" string="预估工期（工艺人员填写）" /> 
                        </group>
                        <!-- <group colspan="2">
                            <field name="gongyishengpi_suoming" widget="text" attrs="{'readonly': [('state','not in',('draft'))]}" /> 
                        </group>
                        <group colspan="2">
                            <field name="touchanshengpi_suoming" widget="text" attrs="{'readonly': [('state','not in',('draft'))]}"/> 
                        </group> -->
                    </group>
                    <notebook>
                        <page string="产品" name="products">
                            <field name="line_ids">
                                <tree string="Products" editable="bottom">
                                    <field name="product_id"
                                        domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                                    <field name="product_text_all" />
                                    <field name="product_description_variants" attrs="{'invisible': [('product_description_variants', '=', '')], 'readonly': [('parent.state', '!=', 'draft')]}"/>
                                    <field name="product_qty"/>
                                    <field name="qty_ordered" optional="show"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="product_uom_id" string="计量单位" groups="uom.group_uom" optional="show" attrs="{'required': [('product_id', '!=', False)]}"/>
                                    <field name="schedule_date" optional="hide"/>
                                    <!-- <field name="demand_qty"/>
                                    <field name="warehouse_in_hand" />
                                    <field name="in_transit" />
                                    <field name="can_borrowed_qty" />
                                    <field name="already_occupied"/>
                                    <field name="purchase_qty" /> -->

                                    <field name="analytic_distribution" widget="analytic_distribution"
                                        optional="hide"
                                        groups="analytic.group_analytic_accounting"
                                        options="{'product_field': 'product_id', 'business_domain': 'purchase_order'}"/>
                                    <field name="price_unit"/>
                                </tree>
                                <form string="Products">
                                    <group>
                                        <field name="product_id"
                                            domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" />
                                        <field name="product_text_all" />
                                        <field name="product_qty"/>
                                        <field name="qty_ordered"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="product_uom_id" groups="uom.group_uom"/>
                                        <field name="schedule_date" string="希望到货日期"/>                           
                                        <field name="analytic_distribution" widget="analytic_distribution"
                                            groups="analytic.group_analytic_accounting"
                                            options="{'product_field': 'product_id', 'business_domain': 'purchase_order'}"/>
                                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                                    </group>
                                </form>
                            </field>
                            <separator string="期限和条件"/>
                            <field name="description" class="oe-bordered-editor" attrs="{'readonly': [('state','not in',('draft','in_progress','open'))]}"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                <field name="message_follower_ids"/>
                <field name="activity_ids"/>
                <field name="message_ids"/>
                </div>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="view_touchan_tree">
            <field name="name">touchan.tree</field>
            <field name="model">purchase.requisition</field>
            <field name="arch" type="xml">
                <tree string="Purchase Agreements" js_class="purchase_requisition_dashboard_list" sample="1">
                    <field name="message_needaction" invisible="1"/>
                    <field name="project_id"/>
                    <field name="name" string="投产审批单"/>
                    <field name="touchan_ren" />
                    <field name="purchase_ids" widget="many2many_tags" string="采购订单"/>
                    <field name="purchase_supplier_id" />
                    <field name="purchase_requisition_qty" />
                    <field name="purchase_check_in_qty" />
                    <field name="is_delay"/>
                    <field name="is_done"/>
                    <field name="user_id" optional="show" widget='many2one_avatar_user'/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="show"/>
                    <field name="schedule_date" string="希望到货日期" optional="show"/>
                    <field name="purchase_check_in_date" optional="show"/>
                    <field name="date_end" optional="show" widget='remaining_days' decoration-danger="date_end and date_end&lt;current_date" attrs="{'invisible': [('state','in', ('done', 'cancel'))]}"/>
                    <field name="state" optional="show" widget='badge' decoration-success="state == 'done'" decoration-info="state not in ('done', 'cancel')"/>
                    <field name="activity_exception_decoration" widget="activity_exception"/>
                </tree>
            </field>
        </record>

        <record id="view_purchase_requisition_kanban_custom" model="ir.ui.view">
            <field name="name">purchase.requisition.kanba.custom</field>
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_kanban"/>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                   <attribute name="action">action_navigation_touchan</attribute>
                   <attribute name="type">object</attribute>
                </xpath>
                <xpath expr="//templates" position="replace">
                   <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings mt4">
                                        <strong class="o_kanban_record_title"><span><field name="name"/></span></strong>
                                    </div>
                                    <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'in_progress': 'default', 'open': 'success', 'done': 'success', 'close': 'danger'}}" readonly="1"/>
                                </div>
                                <div class="o_kanban_record_body">
                                    <span class="text-muted">采购员：<field name="user_id" widget="many2one_avatar_user"/></span>
                                    <span class="text-muted">事由：<field name="reason"/></span>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="text-muted">希望到货日期：<field name="schedule_date"/></span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        
                                        <span class="text-muted">申请人：<field name="applicant_id" widget="many2one_avatar_user"/></span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </xpath>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_touchan_guanli">
            <field name="name">投产单</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.requisition</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[('order_type', '=', 'touchandan')]</field>
            <field name="search_view_id" ref="custom_purchase_requisition.view_touchan_filter"/>
            <field name="context">{'default_order_type':'touchandan','search_default_my_agreements':1,'search_default_my_touchan':1}</field>
            <field name="view_id" ref="view_touchan_tree"/>
            <field name="view_ids" 
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'tree','view_id':ref('view_touchan_tree')}),
                          (0, 0, {'view_mode': 'kanban','view_id':ref('view_purchase_requisition_kanban_custom')}),
                          (0, 0, {'view_mode': 'form', 'view_id': ref('view_touchan_form')})
                        ]"/>
            <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                建立一个新的投产单
            </p>
            </field>
        </record>
        <record model="ir.actions.act_window" id="purchase_requisition.action_purchase_requisition">
            <field name="domain">[('order_type', '=', 'caigoujihuadan')]</field>
            <field name="context">{'search_default_my_agreements':True,'search_default_my_purchase_requisition':True}</field>
        </record>
        <menuitem
            id="menu_touchan_mgt"
            sequence="12"
            parent="purchase.menu_procurement_management"
            action="action_touchan_guanli"/>

        <!-- 简化的BOM树视图，用于采购申请单的BOM选择弹窗 -->
        <record id="mrp_bom_tree_view_simple" model="ir.ui.view">
            <field name="name">mrp.bom.tree.simple</field>
            <field name="model">mrp.bom</field>
            <field name="arch" type="xml">
                <tree string="BOM清单" default_order="sequence, id">
                    <field name="active" invisible="1"/>
                    <field name="sequence" widget="handle"/>
                    <field name="product_tmpl_id" string="产品"/>
                    <field name="code" string="BOM编号"/>
                    <field name="type" string="类型"/>
                    <field name="product_qty" string="数量"/>
                    <field name="product_uom_id" string="单位" groups="uom.group_uom"/>
                </tree>
            </field>
        </record>

        <!-- 简化的BOM搜索视图，用于采购申请单的BOM选择弹窗 -->
        <record id="view_mrp_bom_filter_simple" model="ir.ui.view">
            <field name="name">mrp.bom.select.simple</field>
            <field name="model">mrp.bom</field>
            <field name="arch" type="xml">
                <search string="搜索BOM">
                    <field name="code" string="BOM编号" filter_domain="['|', ('code', 'ilike', self), ('product_tmpl_id', 'ilike', self)]"/>
                    <field name="product_tmpl_id" string="产品"/>
                    <filter string="制造" name="normal" domain="[('type', '=', 'normal')]"/>
                    <filter string="套件" name="phantom" domain="[('type', '=', 'phantom')]"/>
                    <separator/>
                    <filter string="有效" name="active" domain="[('active', '=', True)]" default="true"/>
                    <group expand="0" string="分组...">
                        <filter string="产品" name="group_product" domain="[]" context="{'group_by':'product_tmpl_id'}"/>
                        <filter string="类型" name="group_type" domain="[]" context="{'group_by':'type'}"/>
                    </group>
                </search>
            </field>
        </record>

    </data>
</odoo>