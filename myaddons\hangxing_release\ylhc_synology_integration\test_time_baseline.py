#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间基准逻辑的脚本
验证系统统一从 2025-08-02 10:00:00 开始处理附件

使用方法：
1. 启动Odoo shell: python odoo-bin shell -d your_database
2. 在shell中运行: exec(open('myaddons/hangxing_release/ylhc_synology_integration/test_time_baseline.py').read())
"""

def test_time_baseline():
    """测试时间基准逻辑"""
    print("=== 测试时间基准逻辑 ===")
    
    # 获取模型
    attachment_model = env['ir.attachment']
    config_model = env['synology.attachment.config']
    
    baseline_time = '2025-08-02 10:00:00'
    print(f"时间基准: {baseline_time}")
    
    # 1. 查看现有配置
    print("\n1. 查看同步配置...")
    
    configs = config_model.search([
        ('active', '=', True),
        ('upload_to_synology', '=', True)
    ])
    
    if not configs:
        print("   没有找到活跃的同步配置")
        return
    
    print(f"   找到 {len(configs)} 个配置:")
    
    for config in configs:
        print(f"   配置: {config.model_name}")
        for field_config in config.field_ids.filtered('active'):
            sync_mode = "全量同步" if field_config.sync_existing else "增量同步"
            print(f"     - {field_config.field_name}: {sync_mode}")
    
    # 2. 测试附件查询逻辑
    print(f"\n2. 测试附件查询逻辑...")
    
    for config in configs[:2]:  # 只测试前2个配置
        for field_config in config.field_ids.filtered('active')[:1]:  # 每个配置只测试第一个字段
            print(f"\n   测试: {config.model_name}.{field_config.field_name}")
            
            # 查询基准时间之前的附件
            before_domain = [
                ('res_model', '=', config.model_name),
                ('res_field', '=', field_config.field_name),
                ('type', '=', 'binary'),
                ('create_date', '<=', baseline_time)
            ]
            
            before_count = attachment_model.search_count(before_domain)
            print(f"     基准时间之前的附件: {before_count} 个")
            
            # 查询基准时间之后的附件
            after_domain = [
                ('res_model', '=', config.model_name),
                ('res_field', '=', field_config.field_name),
                ('type', '=', 'binary'),
                ('create_date', '>', baseline_time)
            ]
            
            after_count = attachment_model.search_count(after_domain)
            print(f"     基准时间之后的附件: {after_count} 个")
            
            # 查询需要同步的附件（基准时间之后 + 状态过滤）
            sync_domain = [
                ('res_model', '=', config.model_name),
                ('res_field', '=', field_config.field_name),
                ('type', '=', 'binary'),
                '|',
                ('synology_sync_status', '=', False),
                ('synology_sync_status', 'in', ['pending', 'failed']),
                ('create_date', '>', baseline_time)
            ]
            
            sync_count = attachment_model.search_count(sync_domain)
            print(f"     需要同步的附件: {sync_count} 个")
            
            if sync_count > 0:
                # 显示几个示例
                sample_attachments = attachment_model.search(sync_domain, limit=3)
                print(f"     示例附件:")
                for att in sample_attachments:
                    print(f"       - ID {att.id}: {att.name}, 创建时间: {att.create_date}")

def test_sync_with_baseline():
    """测试带时间基准的同步"""
    print("\n=== 测试带时间基准的同步 ===")
    
    attachment_model = env['ir.attachment']
    sync_log_model = env['synology.sync.log']
    
    # 记录同步前的状态
    before_logs = sync_log_model.search_count([])
    print(f"同步前日志数量: {before_logs}")
    
    try:
        # 运行测试同步
        print("\n运行测试同步...")
        result = attachment_model.with_context(
            test_mode=True,
            max_attachments=3
        ).cron_sync_attachments_to_synology()
        
        print("✓ 测试同步完成")
        
        # 查看新创建的日志
        after_logs = sync_log_model.search_count([])
        new_logs_count = after_logs - before_logs
        
        if new_logs_count > 0:
            latest_log = sync_log_model.search([
                ('sync_type', '=', 'test')
            ], limit=1, order='create_date desc')
            
            if latest_log:
                print(f"\n最新同步日志 (ID: {latest_log.id}):")
                print(f"  同步时间: {latest_log.sync_date}")
                print(f"  处理数量: {latest_log.total_count}")
                print(f"  成功数量: {latest_log.success_count}")
                print(f"  失败数量: {latest_log.failed_count}")
                
                if latest_log.notes:
                    print(f"  备注:")
                    for line in latest_log.notes.split('\n'):
                        if line.strip():
                            print(f"    {line}")
                
                # 验证是否使用了正确的时间基准
                if "2025-08-02 10:00:00" in (latest_log.notes or ""):
                    print("  ✓ 正确使用了时间基准 2025-08-02 10:00:00")
                else:
                    print("  ⚠ 未找到时间基准信息")
        
    except Exception as e:
        print(f"✗ 测试同步失败: {str(e)}")

def show_attachment_timeline():
    """显示附件时间线"""
    print("\n=== 附件时间线分析 ===")
    
    attachment_model = env['ir.attachment']
    baseline_time = '2025-08-02 10:00:00'
    
    # 查询所有二进制附件的时间分布
    all_attachments = attachment_model.search([
        ('type', '=', 'binary')
    ], order='create_date asc')
    
    if not all_attachments:
        print("没有找到二进制附件")
        return
    
    print(f"总共找到 {len(all_attachments)} 个二进制附件")
    
    # 统计时间分布
    before_baseline = 0
    after_baseline = 0
    
    for att in all_attachments:
        if att.create_date <= baseline_time:
            before_baseline += 1
        else:
            after_baseline += 1
    
    print(f"基准时间 {baseline_time} 之前: {before_baseline} 个")
    print(f"基准时间 {baseline_time} 之后: {after_baseline} 个")
    
    # 显示最早和最晚的附件
    if all_attachments:
        earliest = all_attachments[0]
        latest = all_attachments[-1]
        
        print(f"\n最早附件: ID {earliest.id}, 时间: {earliest.create_date}")
        print(f"最晚附件: ID {latest.id}, 时间: {latest.create_date}")
        
        # 显示基准时间附近的附件
        around_baseline = attachment_model.search([
            ('type', '=', 'binary'),
            ('create_date', '>=', '2025-08-02 09:00:00'),
            ('create_date', '<=', '2025-08-02 11:00:00')
        ], order='create_date asc')
        
        if around_baseline:
            print(f"\n基准时间附近的附件 (09:00-11:00):")
            for att in around_baseline[:5]:
                status = "✓ 会处理" if att.create_date > baseline_time else "✗ 不处理"
                print(f"  ID {att.id}: {att.create_date} - {status}")

def main():
    """主测试函数"""
    if 'env' not in globals():
        print("请在Odoo shell中运行此脚本")
        print("启动命令: python odoo-bin shell -d your_database")
        return
    
    print("开始测试时间基准逻辑...")
    
    # 1. 测试时间基准
    test_time_baseline()
    
    # 2. 显示附件时间线
    show_attachment_timeline()
    
    # 3. 测试同步
    test_sync_with_baseline()
    
    print("\n=== 测试总结 ===")
    print("时间基准设置:")
    print("✓ 统一时间基准: 2025-08-02 10:00:00")
    print("✓ 增量同步: 基于上次同步时间，但不早于基准时间")
    print("✓ 全量同步: 也从基准时间开始，不处理更早的附件")
    print("✓ 历史保护: 基准时间之前的附件完全不会被处理")

if __name__ == '__main__':
    main()
