{
    "name": "Ylhc Synology Integration",
    "version": "********.1",
    "category": "Tools",
    "summary": "Automatic Synology NAS integration for attachments",
    "description": """
        This module automatically uploads attachments to Synology NAS based on model configuration.
        It works by intercepting ir.attachment creation and converting binary attachments to URL type.
    """,
    "author": "ylhctec",
    "website": "https://www.ylhctec.com",
    "license": "AGPL-3",
    "depends": ["base", "mail"],
    "data": [
        "security/ir.model.access.csv",
        "data/synology_attachment_config_data.xml",
        "data/synology_sync_log_data.xml",
        "data/synology_cron_data.xml",
        "views/synology_attachment_config_views.xml",
        "views/synology_sync_log_views.xml",
        "views/ir_attachment_views.xml",
        "views/res_config_settings_views.xml",
    ],
    "assets": {
        "web.assets_backend": [
            "ylhc_synology_integration/static/src/js/binary_field_patch.js",
        ],
    },
    "external_dependencies": {
        "python": ["synology_api"],
    },
    "installable": True,
    "application": False,
    "auto_install": False,
}