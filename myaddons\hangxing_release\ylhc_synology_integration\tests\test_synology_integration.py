# -*- coding: utf-8 -*-
"""
Test cases for Synology integration
"""
import unittest
import json
from unittest.mock import patch, MagicMock
from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError


class TestSynologyIntegration(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.attachment_model = self.env['ir.attachment']
        
    def test_synology_fields_exist(self):
        """Test that the new Synology fields exist on ir.attachment"""
        attachment = self.attachment_model.create({
            'name': 'test_file.txt',
            'type': 'url',
            'url': 'http://example.com/test',
            'synology_path': '/test/path/file.txt',
            'synology_share_id': 'share123'
        })
        
        self.assertEqual(attachment.synology_path, '/test/path/file.txt')
        self.assertEqual(attachment.synology_share_id, 'share123')
        
    @patch('myaddons.hangxing_release.ylhc_synology_integration.models.ir_attachment.FileStation')
    def test_unlink_with_synology_data(self, mock_filestation):
        """Test that unlink properly handles Synology files and sharing links"""
        # Mock FileStation instance
        mock_fs = MagicMock()
        mock_filestation.return_value = mock_fs
        mock_fs.delete_shared_link.return_value = {'success': True}
        mock_fs.delete_blocking_function.return_value = {'success': True}
        
        # Mock configuration parameters
        with patch.object(self.env['ir.config_parameter'], 'get_param') as mock_get_param:
            mock_get_param.side_effect = lambda key, default=None: {
                'ylhc_synology_integration.ip_address': '*************',
                'ylhc_synology_integration.port': '5001',
                'ylhc_synology_integration.username': 'admin',
                'ylhc_synology_integration.password': 'password',
                'ylhc_synology_integration.secure': False
            }.get(key, default)
            
            # Create attachment with Synology data
            attachment = self.attachment_model.create({
                'name': 'test_file.txt',
                'type': 'url',
                'url': 'http://synology.local/sharing/test',
                'synology_path': '/test/path/file.txt',
                'synology_share_id': 'share123'
            })
            
            # Delete the attachment
            attachment.unlink()
            
            # Verify that sharing link deletion was called
            mock_fs.delete_shared_link.assert_called_once_with(['share123'])
            
            # Verify that file deletion was called
            mock_fs.delete_blocking_function.assert_called_once_with(['/test/path/file.txt'])
            
    def test_unlink_without_synology_config(self):
        """Test that unlink works when Synology is not configured"""
        with patch.object(self.env['ir.config_parameter'], 'get_param') as mock_get_param:
            mock_get_param.return_value = None  # No configuration
            
            attachment = self.attachment_model.create({
                'name': 'test_file.txt',
                'type': 'binary',
                'datas': b'test content'
            })
            
            # This should work without errors
            attachment.unlink()

    def test_sync_log_creation(self):
        """Test sync log creation and methods"""
        sync_log_model = self.env['synology.sync.log']

        # Test creating a sync log
        sync_log = sync_log_model.create_sync_log(
            sync_type='test',
            model_name='ir.attachment'
        )

        self.assertEqual(sync_log.sync_type, 'test')
        self.assertEqual(sync_log.model_name, 'ir.attachment')
        self.assertTrue(sync_log.sync_date)

        # Test updating results
        attachment_results = {
            1: {
                'success': True,
                'synology_path': '/test/path/file1.txt',
                'url': 'http://example.com/file1',
                'size_mb': 1.5
            },
            2: {
                'success': False,
                'error': 'Upload failed',
                'name': 'file2.txt'
            }
        }

        sync_log.update_results(attachment_results)

        self.assertEqual(sync_log.total_count, 2)
        self.assertEqual(sync_log.success_count, 1)
        self.assertEqual(sync_log.failed_count, 1)
        self.assertEqual(sync_log.success_rate, 50.0)
        self.assertEqual(sync_log.status, 'partial')

        # Test helper methods
        attachment_ids = sync_log.get_attachment_ids_list()
        self.assertEqual(set(attachment_ids), {1, 2})

        success_details = sync_log.get_success_details_dict()
        self.assertIn('1', success_details)
        self.assertEqual(success_details['1']['synology_path'], '/test/path/file1.txt')

        error_details = sync_log.get_error_details_dict()
        self.assertIn('2', error_details)
        self.assertEqual(error_details['2']['error'], 'Upload failed')

        # Test adding notes
        sync_log.add_note('Test note')
        self.assertIn('Test note', sync_log.notes)

        # Test duration setting
        import time
        start_time = time.time() - 5  # 5 seconds ago
        duration = sync_log.set_duration(start_time)
        self.assertGreater(duration, 4)
        self.assertLess(duration, 6)
