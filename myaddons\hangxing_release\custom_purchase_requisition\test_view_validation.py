#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的视图验证脚本
用于验证XML视图定义是否正确
"""

import xml.etree.ElementTree as ET
import sys
import os

def validate_xml_view(file_path):
    """验证XML视图文件"""
    try:
        # 解析XML文件
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        print(f"✅ XML文件语法正确: {file_path}")
        
        # 检查是否是Odoo视图文件
        if root.tag != 'odoo':
            print("⚠️  警告: 根元素不是 'odoo'")
            return False
            
        # 查找视图记录
        view_records = root.findall(".//record[@model='ir.ui.view']")
        print(f"📋 找到 {len(view_records)} 个视图记录")
        
        for record in view_records:
            view_id = record.get('id', '未知ID')
            name_field = record.find("field[@name='name']")
            model_field = record.find("field[@name='model']")
            arch_field = record.find("field[@name='arch']")
            
            view_name = name_field.text if name_field is not None else '未知名称'
            model_name = model_field.text if model_field is not None else '未知模型'
            
            print(f"  📄 视图: {view_id}")
            print(f"     名称: {view_name}")
            print(f"     模型: {model_name}")
            
            if arch_field is not None:
                arch_type = arch_field.get('type', '未知类型')
                print(f"     类型: {arch_type}")
                
                # 检查arch内容
                arch_content = list(arch_field)
                if arch_content:
                    root_element = arch_content[0]
                    print(f"     根元素: {root_element.tag}")
                    
                    # 如果是搜索视图，检查搜索元素
                    if root_element.tag == 'search':
                        fields = root_element.findall('.//field')
                        filters = root_element.findall('.//filter')
                        print(f"     搜索字段: {len(fields)} 个")
                        print(f"     过滤器: {len(filters)} 个")
                        
                        for field in fields:
                            field_name = field.get('name', '未知字段')
                            field_string = field.get('string', '')
                            print(f"       - 字段: {field_name} ({field_string})")
                            
                        for filter_elem in filters:
                            filter_name = filter_elem.get('name', '未知过滤器')
                            filter_string = filter_elem.get('string', '')
                            print(f"       - 过滤器: {filter_name} ({filter_string})")
            
            print()
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML解析错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    view_file = os.path.join(current_dir, "views", "purchase_requisition_views.xml")
    
    if os.path.exists(view_file):
        success = validate_xml_view(view_file)
        sys.exit(0 if success else 1)
    else:
        print(f"❌ 文件不存在: {view_file}")
        sys.exit(1)
