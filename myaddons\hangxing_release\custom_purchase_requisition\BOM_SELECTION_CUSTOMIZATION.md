# BOM选择弹窗自定义说明

## 问题描述
在采购申请单中选择BOM时，原始的搜索弹窗显示了太多不必要的字段，包括：
- 版本
- 状态  
- 归档日期
- BOM类型
- 公司
- 等其他字段

这些字段在BOM选择时并不需要，会影响用户体验。

## 解决方案
创建了简化的BOM选择视图，只显示必要的字段：

### 1. 简化的BOM树视图 (`mrp_bom_tree_view_simple`)
显示字段：
- 产品 (product_tmpl_id)
- BOM编号 (code)
- 类型 (type)
- 数量 (product_qty)
- 单位 (product_uom_id)

### 2. 简化的BOM搜索视图 (`view_mrp_bom_filter_simple`)
搜索功能：
- 按BOM编号搜索
- 按产品搜索
- 按类型过滤（制造/套件）
- 按有效性过滤（默认只显示有效的BOM）
- 按产品和类型分组

### 3. 字段配置修改
在 `purchase_requisition_views.xml` 中修改了 `bom_id` 字段的options属性：

```xml
<field name="bom_id" 
       options="{'no_create': True, 
                 'no_create_edit': True, 
                 'tree_view_ref': 'custom_purchase_requisition.mrp_bom_tree_view_simple', 
                 'search_view_ref': 'custom_purchase_requisition.view_mrp_bom_filter_simple'}"/>
```

## 效果
- 弹窗界面更加简洁
- 只显示BOM选择时需要的关键信息
- 提高用户选择BOM的效率
- 减少界面混乱

## 文件修改
- `views/purchase_requisition_views.xml`: 添加了简化的BOM视图定义，并修改了bom_id字段配置

## 注意事项
- 此修改只影响采购申请单中的BOM选择弹窗
- 不影响其他模块中的BOM视图
- 保持了原有的功能完整性
