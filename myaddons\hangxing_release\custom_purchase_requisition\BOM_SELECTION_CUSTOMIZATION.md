# BOM选择弹窗自定义说明

## 问题描述
在采购申请单中选择BOM时，原始的搜索弹窗显示了太多不必要的字段，包括：
- 版本
- 状态  
- 归档日期
- BOM类型
- 公司
- 等其他字段

这些字段在BOM选择时并不需要，会影响用户体验。

## 解决方案
由于mrp_bom_version模块继承了原始BOM树视图并添加了版本、状态、归档日期等字段，直接通过Many2one字段的options指定自定义视图无法生效。因此采用了以下解决方案：

### 1. 简化的BOM树视图 (`mrp_bom_tree_view_simple`)
显示字段：
- 产品 (product_tmpl_id)
- BOM编号 (code)
- 类型 (type)
- 数量 (product_qty)
- 单位 (product_uom_id)

### 2. 简化的BOM搜索视图 (`view_mrp_bom_filter_simple`)
搜索功能：
- 按BOM编号搜索
- 按产品搜索
- 按类型过滤（制造/套件）
- 按有效性过滤

### 3. 自定义按钮和方法
添加了一个新的"选择BOM"按钮，调用自定义的action方法：

**在视图中：**
```xml
<button name="action_bom_select_simple" type="object" string="选择BOM" class="btn btn-primary"/>
```

**在模型中：**
```python
def action_bom_select_simple(self):
    """打开简化的BOM选择弹窗"""
    return {
        'name': _('选择BOM'),
        'view_mode': 'tree',
        'res_model': 'mrp.bom',
        'view_id': self.env.ref('custom_purchase_requisition.mrp_bom_tree_view_simple').id,
        'search_view_id': self.env.ref('custom_purchase_requisition.view_mrp_bom_filter_simple').id,
        'type': 'ir.actions.act_window',
        'target': 'new',
        'domain': [('active', '=', True)],
        'context': {'create': False, 'edit': False}
    }
```

## 效果
- 弹窗界面更加简洁
- 只显示BOM选择时需要的关键信息
- 提高用户选择BOM的效率
- 减少界面混乱

## 文件修改
- `views/purchase_requisition_views.xml`: 添加了简化的BOM视图定义，并修改了bom_id字段配置

## 注意事项
- 此修改只影响采购申请单中的BOM选择弹窗
- 不影响其他模块中的BOM视图
- 保持了原有的功能完整性
