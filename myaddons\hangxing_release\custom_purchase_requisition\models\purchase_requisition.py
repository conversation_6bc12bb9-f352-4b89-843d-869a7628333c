# _*_ coding: utf-8 _*_
from odoo import api, models, fields , Command, _
from odoo.exceptions import UserError
from dateutil.relativedelta import relativedelta
from odoo.addons.stock.models.stock_move import PROCUREMENT_PRIORITIES

PURCHASE_PRODUCT_TYPE = [
    ('jiegoujian', '机加件'),
    ('feijiegoujian', '非机加件')
]
ORDER_TYPE = [
    ('caigoujihuadan', '采购计划单'),
    ('touchandan', '投产单')
]
PURCHASE_TYPE = [
    ('yuancailiao', '原材料采购'),
    ('gudingzichan', '固定资产采购')
]

class PurchaseRequisition(models.Model):
    _inherit = 'purchase.requisition'
    _description = "采购计划单"
    project_id = fields.Many2one('project.project', '项目', required=True, domain="[('company_id', '=', company_id)]")
    reason = fields.Text("采购事由")
    project_batch_id = fields.Many2one('project.batch', '项目批次')
    @api.depends('project_id')
    def _compute_is_have_project_batch(self):
        for purchase_requisition in self:
            project_id = purchase_requisition.project_id.id
            if project_id == 0:
                purchase_requisition.is_have_project_batch = False
                continue
            module_ids = self.env['project.batch'].search([('project_id', '=', project_id)])
            if len(module_ids)>0:
                 purchase_requisition.is_have_project_batch = True
            else:
                purchase_requisition.is_have_project_batch = False

    is_have_project_batch = fields.Boolean('是否存在项目批次',default=False,compute='_compute_is_have_project_batch')

    purchase_product_type = fields.Selection(PURCHASE_PRODUCT_TYPE,
                              string='采购产品类型', tracking=True, required=True,
                              copy=False, default='feijiegoujian')
    purchase_type = fields.Selection(PURCHASE_TYPE, string='采购类型', default='yuancailiao')

    @api.onchange('company_id')
    def onchange_company_id(self):
        self.project_id = False

    @api.onchange('project_id')
    def _onchange_project_id(self):
        if self.project_id and self.line_ids:
            analytic_account_id = self.project_id.analytic_account_id.id
            for line in self.line_ids:
                line.analytic_distribution = {analytic_account_id:100}
        if not self.project_id:
            self.project_batch_id = False
            return 
        project_batchs = self.env['project.batch'].search([('project_id', '=', self.project_id.id)])
        if len(project_batchs) == 0:
            self.project_batch_id = False
        for project_batch in project_batchs:
            if project_batch.is_default_lot_number:
                self.project_batch_id = project_batch.id
                break

    def _default_approval_id(self):
        user_id = self.env.user
        if self.env.user.department_id and  self.env.user.department_id.manager_id:
            user_id = self.env.user.department_id.manager_id.user_id
        else:
            user_id = self.env.user
        return user_id
    approval_id = fields.Many2one(
        'res.users', string='部门审批人',
        check_company=True,default=_default_approval_id)

    attachment_ids = fields.Many2many('ir.attachment', string='附件')

    user_id = fields.Many2one(
        'res.users', default=False,required=True,string="采购员")
    
    @api.onchange('user_id')
    def _onchange_user_id(self):
        if not self.user_id:
            return 
        if self.order_type != 'touchandan':
            return
        self.touchan_ren = self.user_id
    
    def _default_applicant_id(self):
        user_id = self.env.user
        return user_id
    applicant_id = fields.Many2one(
        'res.users', default=_default_applicant_id,required=True,string="采购申请人")
    
    @api.depends('line_ids.product_qty')
    def _compute_total_product_quantity(selfs):
        for self in selfs:
            lines = self.line_ids
            self.total_product_quantity = sum(lines.mapped('product_qty'))
    total_product_quantity = fields.Float('计划采购数量',compute='_compute_total_product_quantity',store=True)

    @api.depends('line_ids.qty_ordered')
    def _compute_total_ordered_quantity(selfs):
        for self in selfs:
            lines = self.line_ids
            self.total_ordered_quantity = sum(lines.mapped('qty_ordered'))
    total_ordered_quantity = fields.Float('已订购数量',compute='_compute_total_ordered_quantity',store=True)

    @api.depends('total_ordered_quantity','total_product_quantity')
    def _compute_total_qty_ordered_percentage(selfs):
        for self in selfs:
            if self.total_product_quantity:
                total_qty_received_percentage = self.total_ordered_quantity / self.total_product_quantity
            else:
                total_qty_received_percentage = 0.0
            self.total_qty_ordered_percentage = total_qty_received_percentage
    total_qty_ordered_percentage = fields.Float('采购完成率(%)',compute='_compute_total_qty_ordered_percentage',store=True)

    
    supplier_select_id = fields.Many2one('supplier.select', string='供应商选择', copy=False)

    supplier_select_count = fields.Integer(compute='_compute_supplier_select_number', string='选择结果数')

    confirm_date = fields.Date(string='确认时间', tracking=True)

    @api.depends('supplier_select_id')
    def _compute_supplier_select_number(self):
        for purchase_requisition in self:
            if purchase_requisition.supplier_select_id and purchase_requisition.supplier_select_id.state == 'done' :
                purchase_requisition.supplier_select_count = len(purchase_requisition.supplier_select_id)
            else:
                purchase_requisition.supplier_select_count = 0

    # 投产相关
    order_type = fields.Selection(ORDER_TYPE,
                              string='单据类型', tracking=True, required=True,
                              copy=True, default='caigoujihuadan')
    production_file_place = fields.Boolean('投产文件是否已归档',default=True, tracking=True)
    archives_url = fields.Char('关联档案', tracking=True)
    priority = fields.Selection(
        PROCUREMENT_PRIORITIES, string='优先级', default='0')
    touchan_suoming = fields.Text(string='投产说明', tracking=True)
    touchan_ren = fields.Many2one('res.users',string='投产人', tracking=True)
    gongyi_ren = fields.Many2one('res.users',string='工艺人', tracking=True)
    # xiangmushengpi_suoming = fields.Text(string='项目审批说明')
    # touchanshengpi_suoming = fields.Text(string='投产审批说明')
    yugu_gongqi = fields.Char(string='预估工期', tracking=True)
    # gongyishengpi_suoming = fields.Text(string='工艺审批说明')

    @api.depends('line_ids')
    def _compute_purchase_requisition_qty(selfs):
        for self in selfs:
            total_qty = 0
            for line in self.line_ids:
                total_qty += line.product_qty
            self.purchase_requisition_qty = total_qty
    purchase_requisition_qty = fields.Integer(string='计划采购数量',compute='_compute_purchase_requisition_qty',store=True)
    purchase_supplier_id = fields.Many2one('res.partner',string='供应商')
    purchase_check_in_qty = fields.Integer(string='实际到货数量')
    purchase_check_in_date = fields.Date(string='实际到货日期')
    is_delay = fields.Boolean(string='是否超期',default=False)
    is_done = fields.Boolean(string='是否完成',default=False)

    touchan_product = fields.Char('投产产品')
    touchan_cnt = fields.Integer(string='投产数量',default=1)
    jishuwenjian = fields.Char(string='技术文件图代号')

    is_bom_based = fields.Boolean('根据BOM生成', default=False)
    bom_id = fields.Many2one('mrp.bom', string='BOM')

    production_quantity = fields.Float(string="投产数量")

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if 'name' in vals.keys() and vals['name'] == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('purchase.requisition.blanket.order')
            elif not 'name' in vals.keys():
                vals['name'] = self.env['ir.sequence'].next_by_code('purchase.requisition.blanket.order')
        lines = super(PurchaseRequisition,self).create(vals_list)
        return lines

    def action_bom_line_select(self):
        self.ensure_one()
        if not self.bom_id:
            raise UserError(_('请选择BOM'))
        return {
            'name': _('选择BOM清单'),
            'view_mode': 'tree',
            'res_model': 'mrp.bom.line',
            'view_id': self.env.ref('custom_purchase_requisition.view_mrp_bom_line_purchase_tree').id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'domain': [('bom_id', '=', self.bom_id.id)],
            'context': {
                'create': False,
                'group_by': ['product_categ_name'],
                'purchase_requisition_id': self.id,
            }
        }

    def action_navigation_touchan(self):
        view = self.env.ref('purchase_requisition.view_purchase_requisition_form')
        name = '采购计划单审核'
        if self.order_type == 'touchandan':
            view = self.env.ref('custom_purchase_requisition.view_touchan_form')
            name = '投产单审核'
        ctx = dict(
            self.env.context,
        )
        id = self.id
        return {
            'name': _(name),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_id': id,
            'res_model': 'purchase.requisition',
            'views': [(view.id, 'form')],
            'target': 'current',
            'context': ctx,
        }

    def action_in_progress(self):
        res = super().action_in_progress()
        # self.ensure_one()
        # if not self.line_ids and self.order_type != 'touchandan' :
        #     raise UserError(_("没有明细行，无法确认此申请单 '%s'", self.name))
        # if self.type_id.quantity_copy == 'none' and self.vendor_id:
        #     for requisition_line in self.line_ids:
        #         if requisition_line.price_unit <= 0.0:
        #             raise UserError(_('您无法确认没有单价的采购计划单。'))
        #         if requisition_line.product_qty <= 0.0:
        #             raise UserError(_('您无法确认没有数量的采购计划单。'))
        #         requisition_line.create_supplier_info()
        #     self.write({'state': 'ongoing'})
        # else:
        #     self.write({'state': 'in_progress'})
        # Set the sequence number regarding the requisition type
        # if self.name == 'New':
        #     self.name = self.env['ir.sequence'].next_by_code('purchase.requisition.blanket.order')

        self.write({'confirm_date': fields.Date.today(), 'ordering_date': fields.Date.today()})
        activity_vals = []
        schedule__date =''
        if self.schedule_date:
            schedule__date =',希望到货日期:{}'.format(self.schedule_date)
        summary = '申请人:{}{}'.format(self.applicant_id.name,schedule__date)
        activity_vals.append({
                'activity_type_id': self.env.ref('custom_purchase_requisition.mail_activity_puchase_to_do').id,
                'user_id': self.user_id.id,
                'res_id': self.id,
                'res_model_id': self.env.ref('purchase_requisition.model_purchase_requisition').id,
                'summary':summary,
        })
        self.env['mail.activity'].create(activity_vals)
        return res

    
    def action_supplier_select(self):
        module_ids = self.env['supplier.select'].search([('requisition_id', '=', self._ids)],limit=1)
        id_ = False
        if len(module_ids)>0:
           id_ = module_ids[0].id
        else:
            name = "采购计划单:{} 选择供应商".format(self.name)
            new_ids_ = self.env['supplier.select'].create({'name':name,'requisition_id':self._ids})
            self.write({'supplier_select_id':new_ids_})
            id_ = new_ids_.id
        view = self.env.ref('custom_purchase_requisition.view_supplier_select_form')
        ctx = dict(
            self.env.context,
        )
        return {
            'name': _('供应商选择'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list',
            'res_id': id_,
            'res_model': 'supplier.select',
            'views': [(view.id, 'form')],
            'target': 'current',
            'context': ctx,
        }
    
    def action_supplier_select_result(self):
        module_ids = self.env['supplier.select'].search([('requisition_id', '=', self._ids)],limit=1)
        id_ = False
        if len(module_ids)>0:
           id_ = module_ids[0].id
        else:
            raise UserError(_('没有找到对应的选择供应商结构'))
        view = self.env.ref('custom_purchase_requisition.view_supplier_select_form')
        ctx = dict(
            self.env.context,
        )
        return {
            'name': _('供应商选择'),
            'type': 'ir.actions.act_window',
            'view_mode': 'list',
            'res_id': id_,
            'res_model': 'supplier.select',
            'views': [(view.id, 'form')],
            'target': 'current',
            'context': ctx,
        }
    
    def _calc_touchan_shenqing_info(self):
        check_in_date =  fields.Datetime.today() + relativedelta(days=-1000)
        purchase_requisition_ids = self.env['purchase.requisition'].search([('order_type','=','touchandan'),('state','in',('ongoing','in_progress','open','done'))])
        for  purchase_requisition_id in purchase_requisition_ids:
            purchase_ids =   purchase_requisition_id.purchase_ids 
            qty_done = 0
            is_done = True
            is_delay = False
            purchase_supplier_id = False
            for purchase_id in purchase_ids:
                purchase_supplier_id = purchase_id.partner_id
                for picking_id in purchase_id.picking_ids:
                    for move_id in picking_id.move_ids:
                        qty_done += move_id.quantity_done
                    if picking_id.date_done and picking_id.date_done > check_in_date:
                        check_in_date = picking_id.date_done
                    if picking_id.state != 'done':
                        is_done = False
                    if purchase_id.date_planned and picking_id.date_done and purchase_id.date_planned < picking_id.date_done:
                        is_delay = True
            purchase_requisition_vals ={}
            purchase_requisition_vals.update({'purchase_check_in_qty':qty_done})
            purchase_requisition_vals.update({'purchase_check_in_date':check_in_date})
            purchase_requisition_vals.update({'is_delay':is_delay})
            purchase_requisition_vals.update({'is_done':is_done})
            purchase_requisition_vals.update({'purchase_supplier_id':purchase_supplier_id})
            if is_done:
                purchase_requisition_vals.update({'state':'done'})
            purchase_requisition_id.write(purchase_requisition_vals)

    @api.model
    def retrieve_dashboard(self):
        """ This function returns the values to populate the custom dashboard in
            the purchase order views.
        """
        self.check_access_rights('read')

        result = {
            'all_done': 0,
            'all_ongoing': 0,
            'all_late': 0,
        }

        purchase_requisitions = self.env['purchase.requisition']
        result['all_done'] = purchase_requisitions.search_count([('order_type','=','touchandan'),('state', '=', 'done')])
        result['all_ongoing'] =  purchase_requisitions.search_count([('order_type','=','touchandan'),('state', 'in', ('draft','ongoing','in_progress','open'))])
        result['all_late'] = purchase_requisitions.search_count([('state', 'in',('draft','ongoing','in_progress','open')), ('is_delay', '=', True)])
       
        return result
    
   
    def update_demand(self):
        """
            更新需求行数量
        """
        for line in self.line_ids:
            line._compute_demand_qty()
            line._compute_warehouse_in_hand()
            line._compute_in_transit()
            line._compute_pending_production()
            line._compute_already_occupied()
            line._compute_already_occupied_by_others()
            line._compute_available_qty()
            line._compute_purchase_qty()


        

class PurchaseRequisitionLine(models.Model):
    _inherit = 'purchase.requisition.line'
    vendor_id = fields.Many2one('res.partner', string="希望供应商", domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
    product_text_all = fields.Text('产品参数', related="product_id.product_text_all")

    demand_qty = fields.Float(string="需求数量", compute="_compute_demand_qty")
    depletion_qty = fields.Float(string="抛料", compute="_compute_demand_qty")
    warehouse_in_hand = fields.Float(string="在手可用", compute="_compute_warehouse_in_hand")
    in_transit = fields.Float(string="在途", compute="_compute_in_transit")
    pending_production = fields.Float(string="待生产数量", compute="_compute_pending_production")
    already_occupied = fields.Float(string="已占用数量", compute="_compute_already_occupied")
    already_occupied_by_others = fields.Float(string="已被占用数量", compute="_compute_already_occupied_by_others")
    available_qty = fields.Float(string="可用数量", compute="_compute_available_qty")
    purchase_qty = fields.Float(string="采购数量", compute="_compute_purchase_qty")

    @api.onchange('product_id')
    def _onchange_product_id(self):
        super()._onchange_product_id()
        if not self.analytic_distribution and self.requisition_id.project_id:
            analytic_account = self.requisition_id.project_id.analytic_account_id
            self.analytic_distribution = {analytic_account.id:100} 
        if not self.vendor_id:
            self.vendor_id = self.requisition_id.vendor_id  

    @api.depends('requisition_id.bom_id', 'requisition_id.production_quantity', 'product_id')
    def _compute_demand_qty(self):
        """
        计算需求数量：投产数量 * BOM单上的数量 + 产品抛料
        """
        for line in self:
            if not line.requisition_id.bom_id or not line.product_id:
                line.demand_qty = 0
                continue

            # 获取BOM行
            bom_line = line.requisition_id.bom_id.bom_line_ids.filtered(
                lambda l: l.product_id.id == line.product_id.id
            )

            if not bom_line:
                line.demand_qty = 0
                continue

            # 基础需求数量 = BOM数量 * 投产数量
            base_qty = bom_line.product_qty * (line.requisition_id.production_quantity or 1)

            # 计算抛料数量
            depletion_qty = 0
            if line.product_id.categ_id.is_depletion:
                # 查找适用的抛料规则
                depletion_rule = self.env['depletion.rule'].search([
                    ('total_quantity_top', '<', base_qty),
                    ('total_quantity_down', '>=', base_qty)
                ], limit=1)

                if depletion_rule:
                    # 计算抛料数量
                    depletion_qty = base_qty * depletion_rule.depletion_quantity

                    # 抛料数量最小为1
                    if depletion_qty < 1:
                        depletion_qty = 1
                    else:
                        depletion_qty = round(depletion_qty)

                    # 检查上限数量
                    if depletion_rule.upper_quantity > 0 and depletion_qty > depletion_rule.upper_quantity:
                        depletion_qty = depletion_rule.upper_quantity

            # 总需求数量 = 基础数量 + 抛料数量
            line.demand_qty = base_qty + depletion_qty
            line.depletion_qty = depletion_qty

    @api.depends('product_id', 'requisition_id.origin', 'requisition_id.schedule_date')
    def _compute_in_transit(self):
        """
        计算产品可用在途数量：
        在途数量 = 采购订单预计到达时间（验收入库）在最晚需求物料时间点前，未被其他项目、批次占用的物料

        最晚需求物料时间点计算：
        1. 如果源单据有值，查询对应制造单的计划完成时间，然后减去90天
        2. 如果没有源单据值，使用当前采购计划单的希望到货日期作为基准时间
        """
        from datetime import timedelta

        for line in self:
            if not line.product_id:
                line.in_transit = 0.0
                continue

            # 确定基准时间
            base_date = None

            # 1. 检查源单据字段
            if line.requisition_id.origin:
                # 查询对应的制造单
                mrp_production = self.env['mrp.production'].search([
                    ('name', '=', line.requisition_id.origin)
                ], limit=1)

                if mrp_production and mrp_production.date_planned_finished:
                    # 使用制造单的计划完成时间减去90天
                    base_date = mrp_production.date_planned_finished.date() - timedelta(days=90)

            # 2. 如果没有源单据或找不到制造单，使用希望到货日期
            if not base_date and line.requisition_id.schedule_date:
                base_date = line.requisition_id.schedule_date

            # 如果还是没有基准时间，返回0
            if not base_date:
                line.in_transit = 0.0
                continue

            total_in_transit = 0.0

            # 1. 查询采购订单：预计到达时间在基准时间前，且未被其他项目占用
            po_lines = self.env['purchase.order.line'].search([
                ('product_id', '=', line.product_id.id),
                ('order_id.state', 'in', ['purchase']),  # 包含更多状态
                ('date_planned', '<=', base_date)
            ])

            for po_line in po_lines:
                # 检查是否被其他项目、批次占用
                # 这里可以根据具体业务逻辑添加占用检查
                # 暂时简单累加所有符合条件的数量
                total_in_transit += po_line.product_qty

            # 2. 查询其他采购计划单：希望到货日期在基准时间前
            pr_lines = self.env['purchase.requisition.line'].search([
                ('product_id', '=', line.product_id.id),
                ('requisition_id.state', 'in', ['in_progress']),
                ('requisition_id.schedule_date', '<=', base_date),
                ('requisition_id', '!=', line.requisition_id.id)  # 排除当前采购计划单
            ])

            for pr_line in pr_lines:
                # 检查是否被其他项目、批次占用
                # 这里可以根据具体业务逻辑添加占用检查
                total_in_transit += pr_line.product_qty

            line.in_transit = total_in_transit

    @api.depends('product_id', 'requisition_id.origin', 'requisition_id.schedule_date')
    def _compute_pending_production(self):
        """
        计算待生产数量：制造过程中，预计完工入库时间在物料需求最晚时间点之前的制造订单的数量

        最晚需求物料时间点计算：
        1. 如果源单据有值，查询对应制造单的计划完成时间，然后减去90天
        2. 如果没有源单据值，使用当前采购计划单的希望到货日期作为基准时间
        """
        from datetime import timedelta

        for line in self:
            if not line.product_id:
                line.pending_production = 0.0
                continue

            # 确定基准时间（与在途数量计算逻辑相同）
            base_date = None

            # 1. 检查源单据字段
            if line.requisition_id.origin:
                # 查询对应的制造单
                mrp_production = self.env['mrp.production'].search([
                    ('name', '=', line.requisition_id.origin)
                ], limit=1)

                if mrp_production and mrp_production.date_planned_finished:
                    # 使用制造单的计划完成时间减去90天
                    base_date = mrp_production.date_planned_finished.date() - timedelta(days=90)

            # 2. 如果没有源单据或找不到制造单，使用希望到货日期
            if not base_date and line.requisition_id.schedule_date:
                base_date = line.requisition_id.schedule_date

            # 如果还是没有基准时间，返回0
            if not base_date:
                line.pending_production = 0.0
                continue

            total_pending_production = 0.0

            # 查询制造单：预计完工入库时间在基准时间前，且未被其他项目占用
            mrp_productions = self.env['mrp.production'].search([
                ('product_id', '=', line.product_id.id),
                ('state', 'in', ['draft', 'confirmed', 'progress', 'to_close']),  # 有效的制造单状态
                ('date_planned_finished', '<=', base_date)
            ])

            for production in mrp_productions:
                # 检查是否被其他项目、批次占用
                # 这里可以根据具体业务逻辑添加占用检查
                # 暂时简单累加所有符合条件的数量
                total_pending_production += production.product_qty

            line.pending_production = total_pending_production

    @api.depends('product_id')
    def _compute_warehouse_in_hand(self):
        """
        计算库房在手数量：产品在手数量 - 已预留数量
        直接从库存中查询计算真正可用的库存数量
        """
        for line in self:
            if not line.product_id:
                line.warehouse_in_hand = 0.0
                continue

            # 直接从stock.quant表查询产品的库存信息
            # 查询所有内部库存位置的库存数量和预留数量
            quant_domain = [
                ('product_id', '=', line.product_id.id),
                ('location_id.usage', '=', 'internal'),  # 只查询内部库存位置
            ]

            # 使用_read_group汇总库存数量和预留数量
            quant_data = self.env['stock.quant']._read_group(
                quant_domain,
                ['quantity:sum', 'reserved_quantity:sum'],
                []
            )

            # 计算可用库存 = 在手数量 - 已预留数量
            if quant_data:
                total_quantity = quant_data[0]['quantity'] or 0.0
                total_reserved = quant_data[0]['reserved_quantity'] or 0.0
                line.warehouse_in_hand = max(0.0, total_quantity - total_reserved)
            else:
                line.warehouse_in_hand = 0.0

    @api.depends('product_id', 'requisition_id.origin')
    def _compute_already_occupied(self):
        """
        计算已占用数量：当前源单据的制造单已经占用的数量
        如果有源单据，查询对应制造单中该产品的已预留数量
        """
        for line in self:
            if not line.product_id:
                line.already_occupied = 0.0
                continue

            # 检查是否有源单据
            if not line.requisition_id.origin:
                line.already_occupied = 0.0
                continue

            # 查询对应的制造单
            mrp_production = self.env['mrp.production'].search([
                ('name', '=', line.requisition_id.origin)
            ], limit=1)

            if not mrp_production:
                line.already_occupied = 0.0
                continue

            # 查询该制造单中当前产品的原材料需求
            total_occupied = 0.0
            move_raw = mrp_production.move_raw_ids.filtered(
                lambda m: m.product_id.id == line.product_id.id
            )

            for move in move_raw:
                # 累加已预留的数量
                total_occupied += move.reserved_availability

            line.already_occupied = total_occupied

    @api.depends('product_id')
    def _compute_already_occupied_by_others(self):
        """
        计算已被占用数量：被其他需求占用的数量
        直接从库存中查询产品的总已预留数量
        """
        for line in self:
            if not line.product_id:
                line.already_occupied_by_others = 0.0
                continue

            # 直接从stock.quant表查询产品的已预留数量
            # 查询所有内部库存位置的已预留数量
            quant_domain = [
                ('product_id', '=', line.product_id.id),
                ('location_id.usage', '=', 'internal'),  # 只查询内部库存位置
                ('reserved_quantity', '>', 0)  # 只查询有预留数量的记录
            ]

            # 使用_read_group汇总已预留数量
            quant_data = self.env['stock.quant']._read_group(
                quant_domain,
                ['reserved_quantity:sum'],
                []
            )

            # 获取汇总的已预留数量
            if quant_data:
                line.already_occupied_by_others = quant_data[0]['reserved_quantity'] or 0.0
            else:
                line.already_occupied_by_others = 0.0

    @api.depends('warehouse_in_hand', 'already_occupied_by_others', 'in_transit', 'pending_production', 'already_occupied')
    def _compute_available_qty(self):
        """
        计算可用数量：
        原始公式：可用数量 = 在手数量 - 已被占用数量 + 在途数量 + 待生产数量 + 已占用数量

        现在区分两种占用：
        - already_occupied: 当前源单据制造单已占用的数量
        - already_occupied_by_others: 被其他需求占用的数量

        由于warehouse_in_hand已经扣除了总预留数量(already_occupied_by_others)，
        但当前制造单的占用(already_occupied)应该算作可用，所以：
        可用数量 = 在手数量 + 在途数量 + 待生产数量 + 当前制造单已占用数量
        """
        for line in self:
            # 计算可用数量
            line.available_qty = (line.warehouse_in_hand +
                                line.in_transit +
                                line.pending_production +
                                line.already_occupied)

    @api.depends('demand_qty', 'available_qty', 'product_id')
    def _compute_purchase_qty(self):
        """
        计算新采购计划数量：
        新采购计划数量 = max(本次消耗数量 - 可用数量, 最低起订数量, 未设置为0)
        """
        for line in self:
            if not line.product_id:
                line.purchase_qty = 0.0
                continue

            # 计算基础采购数量：本次消耗数量 - 可用数量
            base_purchase_qty = line.demand_qty - line.available_qty

            # 获取产品的最低起订数量（如果有供应商信息的话）
            min_order_qty = 0.0
            if line.product_id.seller_ids:
                # 取第一个供应商的最小订购数量
                supplier_info = line.product_id.seller_ids[0]
                min_order_qty = supplier_info.min_qty or 0.0

            # 计算最终采购数量：取最大值
            line.purchase_qty = max(base_purchase_qty, min_order_qty, 0.0)



