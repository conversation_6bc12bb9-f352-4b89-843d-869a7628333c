<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Daily Sync Attachments to Synology - Main Cron Job -->
        <record id="ir_cron_daily_sync_attachments" model="ir.cron">
            <field name="name">Daily Sync Attachments to Synology</field>
            <field name="model_id" ref="base.model_ir_attachment"/>
            <field name="state">code</field>
            <field name="code">model.cron_sync_attachments_to_synology()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(hours=2)).replace(hour=2, minute=0, second=0, microsecond=0)"/>
            <field name="numbercall">-1</field>
            <field name="active" eval="True"/>
            <field name="doall" eval="False"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="priority">5</field>
        </record>

    </data>
</odoo>
