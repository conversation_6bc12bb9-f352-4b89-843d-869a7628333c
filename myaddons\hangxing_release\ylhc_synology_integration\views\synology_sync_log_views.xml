<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Synology Sync Log Tree View -->
    <record id="view_synology_sync_log_tree" model="ir.ui.view">
        <field name="name">synology.sync.log.tree</field>
        <field name="model">synology.sync.log</field>
        <field name="arch" type="xml">
            <tree string="Synology Sync Logs" default_order="sync_date desc" decoration-success="status=='success'" decoration-warning="status=='partial'" decoration-danger="status=='failed'">
                <field name="sync_date"/>
                <field name="sync_type"/>
                <field name="model_name"/>
                <field name="total_count"/>
                <field name="success_count"/>
                <field name="failed_count"/>
                <field name="success_rate" widget="percentage"/>
                <field name="status"/>
                <field name="duration"/>
                <field name="total_size"/>
                <field name="triggered_by"/>
            </tree>
        </field>
    </record>

    <!-- Synology Sync Log Form View -->
    <record id="view_synology_sync_log_form" model="ir.ui.view">
        <field name="name">synology.sync.log.form</field>
        <field name="model">synology.sync.log</field>
        <field name="arch" type="xml">
            <form string="Synology Sync Log" create="false" edit="false">
                <header>
                    <button name="action_view_attachments" type="object" string="View Attachments" class="btn-primary" attrs="{'invisible': [('total_count', '=', 0)]}"/>
                    <field name="status" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="sync_date" readonly="1"/>
                        </h1>
                        <h2>
                            <field name="sync_type" readonly="1"/>
                        </h2>
                    </div>
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="model_name" readonly="1"/>
                            <field name="field_name" readonly="1"/>
                            <field name="triggered_by" readonly="1"/>
                            <field name="config_id" readonly="1"/>
                        </group>
                        <group name="results" string="Sync Results">
                            <field name="total_count" readonly="1"/>
                            <field name="success_count" readonly="1"/>
                            <field name="failed_count" readonly="1"/>
                            <field name="skipped_count" readonly="1"/>
                            <field name="success_rate" widget="percentage" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <group name="performance" string="Performance">
                            <field name="duration" readonly="1"/>
                            <field name="total_size" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Record IDs" name="record_ids">
                            <field name="res_ids" readonly="1" widget="text"/>
                        </page>
                        <page string="Attachment IDs" name="attachment_ids">
                            <field name="attachment_ids" readonly="1" widget="text"/>
                        </page>
                        <page string="Success Details" name="success_details">
                            <field name="success_details" readonly="1" widget="text"/>
                        </page>
                        <page string="Error Details" name="error_details">
                            <field name="error_details" readonly="1" widget="text"/>
                        </page>
                        <page string="Notes" name="notes">
                            <field name="notes" readonly="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Synology Sync Log Search View -->
    <record id="view_synology_sync_log_search" model="ir.ui.view">
        <field name="name">synology.sync.log.search</field>
        <field name="model">synology.sync.log</field>
        <field name="arch" type="xml">
            <search string="Search Sync Logs">
                <field name="sync_date"/>
                <field name="sync_type"/>
                <field name="model_name"/>
                <field name="triggered_by"/>
                <field name="status"/>
                <separator/>
                <filter string="Today" name="today" domain="[('sync_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('sync_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" domain="[('sync_date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                <separator/>
                <filter string="Success" name="success" domain="[('status', '=', 'success')]"/>
                <filter string="Partial Success" name="partial" domain="[('status', '=', 'partial')]"/>
                <filter string="Failed" name="failed" domain="[('status', '=', 'failed')]"/>
                <separator/>
                <filter string="Manual Sync" name="manual" domain="[('sync_type', '=', 'manual')]"/>
                <filter string="Scheduled Sync" name="scheduled" domain="[('sync_type', '=', 'scheduled')]"/>
                <filter string="API Sync" name="api" domain="[('sync_type', '=', 'api')]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Sync Type" name="group_sync_type" context="{'group_by': 'sync_type'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Model" name="group_model" context="{'group_by': 'model_name'}"/>
                    <filter string="Triggered By" name="group_user" context="{'group_by': 'triggered_by'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'sync_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Synology Sync Log Action -->
    <record id="action_synology_sync_log" model="ir.actions.act_window">
        <field name="name">Synology Sync Logs</field>
        <field name="res_model">synology.sync.log</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_synology_sync_log_search"/>
        <field name="context">{'search_default_this_week': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No sync logs found!
            </p>
            <p>
                Sync logs are automatically created when attachments are synchronized to Synology NAS.
                They help track the synchronization process and troubleshoot any issues.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_synology_sync_log"
              name="Sync Logs"
              parent="menu_synology_main"
              action="action_synology_sync_log"
              sequence="30"/>

</odoo>
