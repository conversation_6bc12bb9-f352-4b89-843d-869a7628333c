# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ylhc_synology_integration
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-16 15:14+0000\n"
"PO-Revision-Date: 2025-07-16 15:14+0000\n"
"Last-Translator: \n"
"Language-Team: Chinese (China)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/wizard/synology_upload_wizard.py:0
#, python-format
msgid "An unexpected error occurred after all retries: %s"
msgstr "多次重试后发生意外错误：%s"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "An unexpected error occurred during upload."
msgstr "上传过程中发生意外错误。"

#. module: ylhc_synology_integration
#: model:ir.model,name:ylhc_synology_integration.model_ir_attachment
msgid "Attachment"
msgstr "附件"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_ylhc_synology_upload_wizard_form
msgid "Cancel"
msgstr "取消"

#. module: ylhc_synology_integration
#: model:ir.model,name:ylhc_synology_integration.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/res_config_settings.py:0
#, python-format
msgid "Connection Failed. An error occurred. Please check your credentials and NAS settings. Error: %s"
msgstr "连接失败。发生错误。请检查您的凭据和NAS设置。错误：%s"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/res_config_settings.py:0
#, python-format
msgid "Connection Failed. Could not connect to %s:%s. Please check the IP address, port, and your network connection."
msgstr "连接失败。无法连接到 %s:%s。请检查IP地址、端口和您的网络连接。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/res_config_settings.py:0
#, python-format
msgid "Connection to Synology NAS was successful!"
msgstr "成功连接到Synology NAS！"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/wizard/synology_upload_wizard.py:0
#, python-format
msgid "Could not create sharing link for the uploaded file. Error: %s"
msgstr "未能创建分享链接：%s"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "Could not delete file from Synology. Please check the logs."
msgstr "无法从Synology删除文件。请检查日志。"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__create_uid
msgid "Created by"
msgstr "创建者"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__create_date
msgid "Created on"
msgstr "创建时间"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "Do you really want to delete this file from Synology? This action cannot be undone."
msgstr "您确定要从Synology删除此文件吗？此操作无法撤销。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/wizard/synology_upload_wizard.py:0
#, python-format
msgid "Failed to upload file to Synology. Error: %s"
msgstr "未能上传至群晖：%s"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__file_data
msgid "File"
msgstr "文件"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__file_name
msgid "File Name"
msgstr "文件名"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "File successfully deleted from Synology."
msgstr "文件已成功从Synology删除。"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__id
msgid "ID"
msgstr "ID"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ylhc_synology_upload_wizard__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/res_config_settings.py:0
#, python-format
msgid "Please fill in all Synology connection details (IP, Port, Username, Password) before testing."
msgstr "测试前请填写所有Synology连接详细信息（IP、端口、用户名、密码）。"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "Please save the record before downloading the file."
msgstr "下载文件前请先保存记录。"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "Please save the record before uploading files."
msgstr "上传文件前请先保存记录。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/res_config_settings.py:0
#, python-format
msgid "Success"
msgstr "成功"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_res_config_settings__synology_destination_path
msgid "Synology Destination Path"
msgstr "Synology目标路径"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#: code:addons/ylhc_synology_integration/wizard/synology_upload_wizard.py:0
#, python-format
msgid "Synology Destination Path is not configured."
msgstr "Synology目标路径未配置。"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.res_config_settings_view_form
msgid "Synology File Station"
msgstr "Synology文件管理器"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_res_config_settings__synology_ip_address
msgid "Synology IP Address"
msgstr "Synology IP地址"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_res_config_settings__synology_password
msgid "Synology Password"
msgstr "Synology密码"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_ir_attachment__synology_path
msgid "Synology Path"
msgstr "Synology路径"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_res_config_settings__synology_port
msgid "Synology Port"
msgstr "Synology端口"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_res_config_settings__synology_username
msgid "Synology Username"
msgstr "Synology用户名"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#: code:addons/ylhc_synology_integration/wizard/synology_upload_wizard.py:0
#, python-format
msgid "Synology connection info (IP, Port, Username) is not configured."
msgstr "Synology连接信息（IP、端口、用户名）未配置。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#: code:addons/ylhc_synology_integration/wizard/synology_upload_wizard.py:0
#, python-format
msgid "The 'synology_api' library is not installed."
msgstr "'synology_api'库未安装。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/res_config_settings.py:0
#, python-format
msgid "The 'synology_api' library is not installed. Please install it via 'pip install synology-api'."
msgstr "'synology_api'库未安装。请通过'pip install synology-api'进行安装。"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_ylhc_synology_upload_wizard_form
msgid "Upload"
msgstr "上传"

#. module: ylhc_synology_integration
#: model:ir.actions.act_window,name:ylhc_synology_integration.action_ylhc_synology_upload_wizard
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_ylhc_synology_upload_wizard_form
msgid "Upload to Synology"
msgstr "上传到Synology"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#: code:addons/ylhc_synology_integration/static/src/js/chatter_integration.js:0
#, python-format
msgid "Uploading files to Synology..."
msgstr "正在上传文件到Synology..."

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_res_config_settings__synology_secure
msgid "Use HTTPS (Secure)"
msgstr "使用HTTPS (安全)"

#. module: ylhc_synology_integration
#: model:ir.model,name:ylhc_synology_integration.model_ylhc_synology_upload_wizard
msgid "Wizard to Upload File to Synology"
msgstr "上传文件到Synology向导"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.res_config_settings_view_form
msgid "Ylhc Synology Integration"
msgstr "Ylhc Synology集成"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_res_config_settings__synology_destination_path
msgid "e.g., /home/<USER>"
msgstr "例如, /home/<USER>"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/xml/chatter_integration.xml:0
#, python-format
msgid "上传文件到 Synology"
msgstr "上传文件到 Synology"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.res_config_settings_view_form
msgid "测试连接"
msgstr "测试连接"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_synology_sync_log
msgid "Sync Logs"
msgstr "同步日志"

#. module: ylhc_synology_integration
#: model:ir.actions.act_window,name:ylhc_synology_integration.action_synology_sync_log
msgid "Synology Sync Logs"
msgstr "群晖同步日志"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_sync_log_tree
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_sync_log_form
msgid "Synology Sync Log"
msgstr "群晖同步日志"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__sync_type
msgid "Sync Type"
msgstr "同步类型"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__sync_date
msgid "Sync Date"
msgstr "同步时间"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__total_count
msgid "Total Count"
msgstr "总数量"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__success_count
msgid "Success Count"
msgstr "成功数量"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__failed_count
msgid "Failed Count"
msgstr "失败数量"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__success_rate
msgid "Success Rate (%)"
msgstr "成功率 (%)"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__status
msgid "Status"
msgstr "状态"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__duration
msgid "Duration (seconds)"
msgstr "持续时间(秒)"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_sync_log__total_size
msgid "Total Size (MB)"
msgstr "总大小(MB)"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_sync_log_form
msgid "View Attachments"
msgstr "查看附件"

#. module: ylhc_synology_integration
#: model:ir.cron,name:ylhc_synology_integration.ir_cron_daily_sync_attachments
msgid "Daily Sync Attachments to Synology"
msgstr "每日同步附件到群晖"

#. module: ylhc_synology_integration
#: model:ir.cron,name:ylhc_synology_integration.ir_cron_hourly_sync_attachments
msgid "Hourly Sync Attachments to Synology"
msgstr "每小时同步附件到群晖"

#. module: ylhc_synology_integration
#: model:ir.cron,name:ylhc_synology_integration.ir_cron_weekly_sync_attachments
msgid "Weekly Sync Attachments to Synology"
msgstr "每周同步附件到群晖"

#. module: ylhc_synology_integration
#: model:ir.cron,name:ylhc_synology_integration.ir_cron_manual_sync_test
msgid "Manual Test Sync Attachments to Synology"
msgstr "手动测试同步附件到群晖"

#. module: ylhc_synology_integration
#: model:ir.cron,name:ylhc_synology_integration.ir_cron_cleanup_sync_logs
msgid "Cleanup Old Synology Sync Logs"
msgstr "清理旧的群晖同步日志"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_synology_cron_jobs
msgid "Scheduled Tasks"
msgstr "定时任务"

#. module: ylhc_synology_integration
#: model:ir.actions.act_window,name:ylhc_synology_integration.action_synology_cron_jobs
msgid "Synology Cron Jobs"
msgstr "群晖定时任务"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_sync_quick_actions
msgid "Quick Actions"
msgstr "快速操作"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_enable_daily_sync
#: model:ir.actions.server,name:ylhc_synology_integration.action_enable_daily_sync
msgid "Enable Daily Sync"
msgstr "启用每日同步"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_enable_hourly_sync
#: model:ir.actions.server,name:ylhc_synology_integration.action_enable_hourly_sync
msgid "Enable Hourly Sync"
msgstr "启用每小时同步"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_disable_all_sync
#: model:ir.actions.server,name:ylhc_synology_integration.action_disable_all_sync
msgid "Disable All Sync"
msgstr "禁用所有同步"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_cron_form
msgid "Run Now"
msgstr "立即运行"

#. module: ylhc_synology_integration
#: model:ir.model,name:ylhc_synology_integration.model_synology_attachment_config
msgid "Synology Attachment Configuration"
msgstr "群晖附件配置"

#. module: ylhc_synology_integration
#: model:ir.model,name:ylhc_synology_integration.model_synology_config_setup
msgid "Synology Configuration Setup"
msgstr "群晖配置设置"

#. module: ylhc_synology_integration
#: model:ir.actions.act_window,name:ylhc_synology_integration.action_synology_attachment_config
msgid "Synology Attachment Configuration"
msgstr "群晖附件配置"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_tree
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Synology Attachment Configuration"
msgstr "群晖附件配置"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_synology_main
msgid "Synology Integration"
msgstr "群晖集成"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_synology_attachment_config
msgid "Attachment Configuration"
msgstr "附件配置"

#. module: ylhc_synology_integration
#: model:ir.ui.menu,name:ylhc_synology_integration.menu_synology_quick_config
msgid "Quick Setup"
msgstr "快速设置"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__sequence
msgid "Sequence"
msgstr "序号"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__model_id
msgid "Model"
msgstr "模型"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__model_name
msgid "Model Name"
msgstr "模型名称"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__model_display_name
msgid "Model Display Name"
msgstr "模型显示名称"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__active
msgid "Active"
msgstr "启用"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__upload_to_synology
msgid "Upload to Synology"
msgstr "上传到群晖"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__subfolder_pattern
msgid "Subfolder Pattern"
msgstr "子文件夹模式"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__file_size_limit
msgid "File Size Limit (MB)"
msgstr "文件大小限制 (MB)"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__allowed_extensions
msgid "Allowed Extensions"
msgstr "允许的扩展名"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__excluded_extensions
msgid "Excluded Extensions"
msgstr "排除的扩展名"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__description
msgid "Description"
msgstr "描述"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__notes
msgid "Notes"
msgstr "备注"

#. module: ylhc_synology_integration
#: model:ir.model.fields,field_description:ylhc_synology_integration.field_synology_attachment_config__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__sequence
msgid "Sequence for ordering the configuration records"
msgstr "配置记录的排序序号"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__model_id
msgid "The model whose attachments should be managed"
msgstr "需要管理附件的模型"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__model_name
msgid "Technical name of the model"
msgstr "模型的技术名称"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__model_display_name
msgid "Human readable name of the model"
msgstr "模型的可读名称"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__active
msgid "If unchecked, attachments for this model will not be uploaded to Synology"
msgstr "如果未选中，此模型的附件将不会上传到群晖"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__upload_to_synology
msgid "Whether attachments for this model should be uploaded to Synology NAS"
msgstr "此模型的附件是否应该上传到群晖NAS"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__subfolder_pattern
msgid "Custom subfolder pattern for organizing files. Use {model}, {id}, {name} as placeholders"
msgstr "用于组织文件的自定义子文件夹模式。使用 {model}, {id}, {name} 作为占位符"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__file_size_limit
msgid "Maximum file size in MB. 0 means no limit"
msgstr "最大文件大小（MB）。0表示无限制"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__allowed_extensions
msgid "Comma-separated list of allowed file extensions (e.g., pdf,doc,docx). Leave empty for all types"
msgstr "允许的文件扩展名列表，用逗号分隔（例如：pdf,doc,docx）。留空表示允许所有类型"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__excluded_extensions
msgid "Comma-separated list of excluded file extensions (e.g., tmp,log). Takes precedence over allowed extensions"
msgstr "排除的文件扩展名列表，用逗号分隔（例如：tmp,log）。优先级高于允许的扩展名"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__description
msgid "Description of this configuration"
msgstr "此配置的描述"

#. module: ylhc_synology_integration
#: model:ir.model.fields,help:ylhc_synology_integration.field_synology_attachment_config__notes
msgid "Additional notes for this configuration"
msgstr "此配置的附加备注"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Enable Upload"
msgstr "启用上传"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Disable Upload"
msgstr "禁用上传"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Basic Configuration"
msgstr "基本配置"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "File Filters"
msgstr "文件过滤"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Advanced Configuration"
msgstr "高级配置"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Description & Notes"
msgstr "描述和备注"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Available placeholders: {model}, {id}, {name}, {year}, {month}, {day}"
msgstr "可用占位符：{model}, {id}, {name}, {year}, {month}, {day}"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "Search Synology Attachment Configuration"
msgstr "搜索群晖附件配置"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "Upload Enabled"
msgstr "上传已启用"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "Upload Disabled"
msgstr "上传已禁用"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "Inactive"
msgstr "未启用"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "With Size Limit"
msgstr "有大小限制"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "With Extension Filter"
msgstr "有扩展名过滤"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "Group By"
msgstr "分组"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "Upload Status"
msgstr "上传状态"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_search
msgid "Active Status"
msgstr "启用状态"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.action_synology_attachment_config
msgid "Configure which models' attachments should be uploaded to Synology NAS"
msgstr "配置哪些模型的附件应该上传到群晖NAS"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.action_synology_attachment_config
msgid "Create configuration rules to control which models' attachments are automatically uploaded to your Synology NAS. You can set file size limits, allowed/excluded file extensions, and custom folder structures."
msgstr "创建配置规则来控制哪些模型的附件自动上传到您的群晖NAS。您可以设置文件大小限制、允许/排除的文件扩展名和自定义文件夹结构。"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_tree
msgid "Toggle Upload to Synology"
msgstr "切换群晖上传"

#. module: ylhc_synology_integration
#: model:ir.actions.server,name:ylhc_synology_integration.action_synology_quick_config
msgid "Quick Setup Common Models"
msgstr "快速设置常用模型"

#. module: ylhc_synology_integration
#: model:ir.actions.server,name:ylhc_synology_integration.action_setup_default_configs
msgid "Setup Default Configurations"
msgstr "设置默认配置"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/synology_attachment_config.py:0
#, python-format
msgid "Configuration for model '%s' already exists. Please edit the existing configuration instead of creating a new one."
msgstr "模型 '%s' 的配置已存在。请编辑现有配置而不是创建新配置。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/models/synology_attachment_config.py:0
#, python-format
msgid "File size limit cannot be negative"
msgstr "文件大小限制不能为负数"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#, python-format
msgid "This model is configured not to upload to Synology NAS."
msgstr "此模型配置为不上传到群晖NAS。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#, python-format
msgid "File does not meet the upload criteria for this model."
msgstr "文件不符合此模型的上传条件。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#, python-format
msgid "Temporary models are not uploaded to Synology NAS."
msgstr "临时模型不会上传到群晖NAS。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#, python-format
msgid "This model is not configured for Synology upload."
msgstr "此模型未配置群晖上传。"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/controllers/main.py:0
#, python-format
msgid "Configuration check failed, not uploading to Synology."
msgstr "配置检查失败，不上传到群晖。"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/chatter_integration.js:0
#, python-format
msgid "files skipped (not configured for Synology upload)"
msgstr "文件已跳过（未配置群晖上传）"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/chatter_integration.js:0
#, python-format
msgid "files uploaded successfully to Synology."
msgstr "文件已成功上传到群晖。"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/chatter_integration.js:0
#, python-format
msgid "files failed to upload"
msgstr "文件上传失败"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/chatter_integration.js:0
#, python-format
msgid "Failed to upload"
msgstr "上传失败"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "files failed to upload."
msgstr "文件上传失败。"

#. module: ylhc_synology_integration
#. odoo-javascript
#: code:addons/ylhc_synology_integration/static/src/js/binary_field_patch.js:0
#, python-format
msgid "files uploaded successfully."
msgstr "文件上传成功。"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "e.g., {model}/{name} or documents/{id}"
msgstr "例如：{model}/{name} 或 documents/{id}"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "e.g., pdf,doc,docx,xls,xlsx"
msgstr "例如：pdf,doc,docx,xls,xlsx"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "e.g., tmp,log,cache"
msgstr "例如：tmp,log,cache"

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Description of this configuration..."
msgstr "此配置的描述..."

#. module: ylhc_synology_integration
#: model_terms:ir.ui.view,arch_db:ylhc_synology_integration.view_synology_attachment_config_form
msgid "Additional notes..."
msgstr "附加备注..."

#. module: ylhc_synology_integration
#: model:synology.attachment.config,description:ylhc_synology_integration.config_res_partner
msgid "客户和供应商相关文档上传到群晖存储"
msgstr "客户和供应商相关文档上传到群晖存储"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/views/synology_attachment_config_views.xml:0
#, python-format
msgid "Configuration Setup"
msgstr "配置设置"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/views/synology_attachment_config_views.xml:0
#, python-format
msgid "Setup complete. Created %d configuration(s)"
msgstr "设置完成。已创建 %d 个配置"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/views/synology_attachment_config_views.xml:0
#, python-format
msgid "Quick Setup Complete"
msgstr "快速设置完成"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/views/synology_attachment_config_views.xml:0
#, python-format
msgid "Created %d configuration(s)"
msgstr "已创建 %d 个配置"

#. module: ylhc_synology_integration
#. odoo-python
#: code:addons/ylhc_synology_integration/views/synology_attachment_config_views.xml:0
#, python-format
msgid "All configurations already exist"
msgstr "所有配置已存在"