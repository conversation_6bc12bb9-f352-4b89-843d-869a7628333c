#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的同步逻辑的脚本
验证基于同步日志时间的附件查询功能

使用方法：
1. 启动Odoo shell: python odoo-bin shell -d your_database
2. 在shell中运行: exec(open('myaddons/hangxing_release/ylhc_synology_integration/test_sync_logic.py').read())
"""

def test_sync_log_time_logic():
    """测试基于同步日志时间的查询逻辑"""
    print("=== 测试同步日志时间逻辑 ===")
    
    # 获取模型
    sync_log_model = env['synology.sync.log']
    attachment_model = env['ir.attachment']
    config_model = env['synology.attachment.config']
    
    # 1. 创建测试同步日志
    print("\n1. 创建测试同步日志...")
    
    test_logs = []
    for i in range(3):
        log = sync_log_model.create_sync_log(
            sync_type='test',
            model_name='purchase.order',
            field_name='datas'
        )
        test_logs.append(log)
        print(f"   创建日志 {i+1}: ID={log.id}, 时间={log.create_date}")
    
    # 2. 测试查询最新日志
    print("\n2. 测试查询最新同步日志...")
    
    latest_log = sync_log_model.search([
        ('model_name', '=', 'purchase.order'),
        ('field_name', '=', 'datas'),
        ('sync_type', 'in', ['scheduled', 'manual', 'api', 'test'])
    ], limit=1, order='create_date desc')
    
    if latest_log:
        print(f"   最新日志: ID={latest_log.id}, 时间={latest_log.create_date}")
    else:
        print("   未找到同步日志")
    
    # 3. 测试查询第二新的日志（跳过最新的）
    print("\n3. 测试查询第二新的日志...")
    
    second_latest_log = sync_log_model.search([
        ('model_name', '=', 'purchase.order'),
        ('field_name', '=', 'datas'),
        ('sync_type', 'in', ['scheduled', 'manual', 'api', 'test'])
    ], limit=1, order='create_date desc', offset=1)
    
    if second_latest_log:
        print(f"   第二新日志: ID={second_latest_log.id}, 时间={second_latest_log.create_date}")
        last_sync_time = second_latest_log.create_date
    else:
        print("   未找到第二新的日志，使用默认时间")
        last_sync_time = '2025-08-02 18:00:00'
    
    # 4. 测试附件查询逻辑
    print(f"\n4. 测试附件查询逻辑 (时间限制: {last_sync_time})...")
    
    # 查询在指定时间之后创建的附件
    domain = [
        ('res_model', '=', 'purchase.order'),
        ('res_field', '=', 'datas'),
        ('type', '=', 'binary'),
        '|',
        ('synology_sync_status', '=', False),
        ('synology_sync_status', 'in', ['pending', 'failed']),
        ('create_date', '>', last_sync_time)
    ]
    
    attachments = attachment_model.search(domain, limit=10)
    print(f"   找到 {len(attachments)} 个符合条件的附件")
    
    for att in attachments[:3]:  # 只显示前3个
        print(f"   - 附件 {att.id}: {att.name}, 创建时间: {att.create_date}")
    
    # 5. 测试配置查询
    print("\n5. 测试配置查询...")
    
    configs = config_model.search([
        ('active', '=', True),
        ('upload_to_synology', '=', True)
    ])
    
    print(f"   找到 {len(configs)} 个活跃的配置")
    
    for config in configs:
        print(f"   配置: {config.model_name}")
        for field_config in config.field_ids.filtered('active'):
            print(f"     - 字段: {field_config.field_name}, 同步已存在: {field_config.sync_existing}")
    
    return {
        'test_logs': test_logs,
        'latest_log': latest_log,
        'second_latest_log': second_latest_log,
        'last_sync_time': last_sync_time,
        'attachments': attachments,
        'configs': configs
    }

def test_cron_sync_simulation():
    """模拟定时任务同步过程"""
    print("\n=== 模拟定时任务同步过程 ===")
    
    attachment_model = env['ir.attachment']
    
    print("\n1. 运行测试模式的定时同步...")
    
    try:
        # 运行测试模式的同步
        result = attachment_model.with_context(
            test_mode=True,
            max_attachments=3
        ).cron_sync_attachments_to_synology()
        
        print("   ✓ 测试同步完成")
        
        # 查看最新创建的同步日志
        sync_log_model = env['synology.sync.log']
        recent_logs = sync_log_model.search([
            ('sync_type', '=', 'test')
        ], limit=5, order='create_date desc')
        
        print(f"\n2. 查看最新的 {len(recent_logs)} 个测试同步日志:")
        for log in recent_logs:
            print(f"   日志 {log.id}:")
            print(f"     模型: {log.model_name}")
            print(f"     字段: {log.field_name}")
            print(f"     时间: {log.create_date}")
            print(f"     状态: {log.status}")
            print(f"     处理数量: {log.total_count}")
            if log.notes:
                print(f"     备注: {log.notes}")
            print()
        
    except Exception as e:
        print(f"   ✗ 测试同步失败: {str(e)}")

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    sync_log_model = env['synology.sync.log']
    
    # 删除测试日志
    test_logs = sync_log_model.search([('sync_type', '=', 'test')])
    count = len(test_logs)
    
    if count > 0:
        test_logs.unlink()
        print(f"   已删除 {count} 个测试同步日志")
    else:
        print("   没有找到测试同步日志")

def main():
    """主测试函数"""
    if 'env' not in globals():
        print("请在Odoo shell中运行此脚本")
        print("启动命令: python odoo-bin shell -d your_database")
        return
    
    print("开始测试新的同步逻辑...")
    
    # 1. 测试同步日志时间逻辑
    result = test_sync_log_time_logic()
    
    # 2. 模拟定时任务同步
    test_cron_sync_simulation()
    
    print("\n=== 测试完成 ===")
    print("新的同步逻辑特点:")
    print("1. 为每个字段配置创建单独的同步日志")
    print("2. 基于最新同步日志的创建时间来确定时间范围")
    print("3. 只处理上次同步之后创建的附件")
    print("4. 提供详细的处理结果和错误信息")
    
    # 询问是否清理测试数据
    print("\n是否要清理测试数据？")
    print("取消注释下面的行来清理:")
    print("# cleanup_test_data()")
    
    return result

if __name__ == '__main__':
    main()
